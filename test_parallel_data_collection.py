"""
اختبار جمع البيانات المتوازي لـ 5 أزواج
Parallel Data Collection Test for 5 Pairs
"""
import sys
import time
import asyncio
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any

# إضافة المجلد الحالي إلى مسار Python
sys.path.append(str(Path(__file__).parent))

from BinaryOptionsToolsV2.pocketoption import PocketOption
from config.settings import get_current_ssid, INITIAL_TEST_PAIRS
from config.database import initialize_databases, get_db_session
from database.repositories import CurrencyPairRepository, CandleRepository
from utils.logger import get_logger, log_data_collection

logger = get_logger(__name__)

class ParallelDataCollectionTest:
    """فئة اختبار جمع البيانات المتوازي"""
    
    def __init__(self):
        self.ssid = get_current_ssid()
        self.api = None
        self.test_pairs = INITIAL_TEST_PAIRS
        self.results = {}
        
    async def initialize(self):
        """تهيئة النظام"""
        try:
            logger.info("🚀 تهيئة اختبار جمع البيانات المتوازي")
            
            # تهيئة قواعد البيانات
            await initialize_databases()
            
            # إنشاء عميل الاتصال
            self.api = PocketOption(self.ssid)
            
            # انتظار تأسيس الاتصال
            await asyncio.sleep(5)
            
            logger.info("✅ تم تهيئة النظام بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في تهيئة النظام: {e}")
            return False
    
    async def test_parallel_historical_data(self) -> Dict[str, Any]:
        """اختبار جمع البيانات التاريخية بشكل متوازي"""
        logger.info("📊 بدء اختبار جمع البيانات التاريخية المتوازي")
        
        results = {
            'success': False,
            'pairs_data': {},
            'total_time': 0,
            'data_consistency': True,
            'errors': []
        }
        
        start_time = time.time()
        
        try:
            # إنشاء مهام متوازية لجمع البيانات
            tasks = []
            for pair in self.test_pairs:
                task = asyncio.create_task(self._collect_pair_data(pair))
                tasks.append(task)
            
            # تنفيذ جميع المهام بشكل متوازي
            logger.info(f"⏳ جمع البيانات لـ {len(self.test_pairs)} أزواج بشكل متوازي...")
            pair_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # تحليل النتائج
            for i, result in enumerate(pair_results):
                pair = self.test_pairs[i]
                
                if isinstance(result, Exception):
                    logger.error(f"❌ خطأ في جمع بيانات {pair}: {result}")
                    results['errors'].append(f"{pair}: {str(result)}")
                    results['pairs_data'][pair] = {'success': False, 'error': str(result)}
                else:
                    results['pairs_data'][pair] = result
                    if result['success']:
                        logger.info(f"✅ {pair}: {result['candles_count']} شمعة")
                    else:
                        logger.error(f"❌ {pair}: {result.get('error', 'خطأ غير معروف')}")
            
            # حساب الوقت الإجمالي
            results['total_time'] = time.time() - start_time
            
            # فحص تناسق البيانات
            results['data_consistency'] = self._check_data_consistency(results['pairs_data'])
            
            # تحديد النجاح العام
            successful_pairs = sum(1 for data in results['pairs_data'].values() if data.get('success', False))
            results['success'] = successful_pairs >= len(self.test_pairs) * 0.8  # 80% نجاح
            
            logger.info(f"📊 النتائج: {successful_pairs}/{len(self.test_pairs)} أزواج نجحت")
            logger.info(f"⏱️ الوقت الإجمالي: {results['total_time']:.2f} ثانية")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ خطأ في اختبار جمع البيانات المتوازي: {e}")
            results['errors'].append(str(e))
            return results
    
    async def _collect_pair_data(self, pair: str) -> Dict[str, Any]:
        """جمع البيانات لزوج واحد"""
        result = {
            'success': False,
            'candles_count': 0,
            'timeframe': 300,
            'period': 3600,
            'first_candle_time': None,
            'last_candle_time': None,
            'error': None
        }
        
        try:
            # جمع البيانات التاريخية
            candles = self.api.get_candles(pair, 300, 3600)  # 5 دقائق، ساعة واحدة
            
            if candles and len(candles) > 0:
                result['success'] = True
                result['candles_count'] = len(candles)
                result['first_candle_time'] = candles[0].get('time')
                result['last_candle_time'] = candles[-1].get('time')
                
                # حفظ البيانات في قاعدة البيانات
                await self._save_candles_to_db(pair, candles)
                
                log_data_collection(pair, len(candles), "نجح")
            else:
                result['error'] = "لم يتم جلب أي بيانات"
                log_data_collection(pair, 0, "فشل - لا توجد بيانات")
            
        except Exception as e:
            result['error'] = str(e)
            log_data_collection(pair, 0, f"فشل - {str(e)}")
        
        return result
    
    async def _save_candles_to_db(self, pair_symbol: str, candles: List[Dict[str, Any]]):
        """حفظ الشموع في قاعدة البيانات"""
        try:
            session = get_db_session()
            
            # الحصول على زوج العملة أو إنشاؤه
            pair_repo = CurrencyPairRepository(session)
            pair = pair_repo.get_or_create(pair_symbol)
            
            # حفظ الشموع
            candle_repo = CandleRepository(session)
            
            for candle_data in candles:
                try:
                    timestamp = datetime.fromtimestamp(candle_data['time'])
                    
                    candle_repo.create(
                        pair_id=pair.id,
                        timestamp=timestamp,
                        open_price=float(candle_data['open']),
                        high_price=float(candle_data['high']),
                        low_price=float(candle_data['low']),
                        close_price=float(candle_data['close']),
                        volume=float(candle_data.get('volume', 0)),
                        timeframe=300
                    )
                except Exception as e:
                    logger.warning(f"تخطي شمعة لـ {pair_symbol}: {e}")
                    continue
            
            session.close()
            
        except Exception as e:
            logger.error(f"خطأ في حفظ بيانات {pair_symbol}: {e}")
    
    def _check_data_consistency(self, pairs_data: Dict[str, Dict[str, Any]]) -> bool:
        """فحص تناسق البيانات بين الأزواج"""
        successful_pairs = [data for data in pairs_data.values() if data.get('success', False)]
        
        if len(successful_pairs) < 2:
            return True  # لا يمكن فحص التناسق مع أقل من زوجين
        
        # فحص عدد الشموع
        candle_counts = [data['candles_count'] for data in successful_pairs]
        min_count = min(candle_counts)
        max_count = max(candle_counts)
        
        # السماح بتفاوت 5% في عدد الشموع
        tolerance = max_count * 0.05
        
        if max_count - min_count > tolerance:
            logger.warning(f"⚠️ تفاوت في عدد الشموع: {min_count} - {max_count}")
            return False
        
        logger.info(f"✅ تناسق البيانات جيد: {min_count} - {max_count} شمعة")
        return True
    
    def print_results(self, results: Dict[str, Any]):
        """طباعة النتائج"""
        print("\n" + "="*60)
        print("📊 نتائج اختبار جمع البيانات المتوازي")
        print("="*60)
        
        print(f"\n⏱️ الوقت الإجمالي: {results['total_time']:.2f} ثانية")
        print(f"🎯 النجاح العام: {'✅ نجح' if results['success'] else '❌ فشل'}")
        print(f"📈 تناسق البيانات: {'✅ جيد' if results['data_consistency'] else '❌ ضعيف'}")
        
        print(f"\n📋 تفاصيل الأزواج:")
        for pair, data in results['pairs_data'].items():
            if data.get('success', False):
                print(f"  ✅ {pair}: {data['candles_count']} شمعة")
            else:
                print(f"  ❌ {pair}: {data.get('error', 'خطأ غير معروف')}")
        
        if results['errors']:
            print(f"\n❌ الأخطاء:")
            for error in results['errors']:
                print(f"  - {error}")
        
        print("\n" + "="*60)

async def main():
    """الدالة الرئيسية"""
    print("🧪 بدء اختبار جمع البيانات المتوازي لـ 5 أزواج")
    
    tester = ParallelDataCollectionTest()
    
    # تهيئة النظام
    if not await tester.initialize():
        print("❌ فشل في تهيئة النظام")
        return False
    
    # تشغيل الاختبار
    results = await tester.test_parallel_historical_data()
    
    # طباعة النتائج
    tester.print_results(results)
    
    # تحديد النجاح
    if results['success'] and results['data_consistency']:
        print("🎉 الاختبار نجح! يمكن المتابعة إلى 30 زوج")
        return True
    else:
        print("⚠️ الاختبار فشل أو يحتاج تحسين")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
