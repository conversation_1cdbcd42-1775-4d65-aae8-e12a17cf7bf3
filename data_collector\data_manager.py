"""
مدير البيانات
Data Manager
"""
import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set
from pathlib import Path

from config.settings import trading_config, INITIAL_TEST_PAIRS
from config.database import get_async_redis_client, get_async_db_session
from database.repositories import CurrencyPairRepository, CandleRepository
from utils.logger import get_logger, log_data_collection
from .pocket_option_client import PocketOptionClient

logger = get_logger(__name__)

class DataManager:
    """مدير البيانات الرئيسي"""
    
    def __init__(self):
        self.client = PocketOptionClient()
        self.redis_client = None
        self.active_pairs = set(INITIAL_TEST_PAIRS)  # البدء بـ 5 أزواج للاختبار
        self.running = False
        self.data_collection_tasks = {}
        self.live_streaming_task = None
        
    async def initialize(self) -> bool:
        """تهيئة مدير البيانات"""
        try:
            logger.info("🚀 تهيئة مدير البيانات...")
            
            # الاتصال بالمنصة
            if not await self.client.connect():
                logger.error("❌ فشل في الاتصال بالمنصة")
                return False
            
            # الاتصال بـ Redis
            self.redis_client = get_async_redis_client()
            
            # اختبار Redis
            await self.redis_client.ping()
            
            logger.info("✅ تم تهيئة مدير البيانات بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في تهيئة مدير البيانات: {e}")
            return False
    
    async def start_data_collection(self) -> bool:
        """بدء جمع البيانات"""
        try:
            if self.running:
                logger.warning("⚠️ جمع البيانات يعمل بالفعل")
                return True
            
            logger.info("📊 بدء جمع البيانات...")
            self.running = True
            
            # جمع البيانات التاريخية الأولية
            await self._collect_initial_historical_data()
            
            # بدء البث المباشر
            await self._start_live_streaming()
            
            # بدء مهام الصيانة
            asyncio.create_task(self._maintenance_loop())
            
            logger.info("✅ تم بدء جمع البيانات بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في بدء جمع البيانات: {e}")
            self.running = False
            return False
    
    async def _collect_initial_historical_data(self):
        """جمع البيانات التاريخية الأولية"""
        try:
            logger.info("📈 جمع البيانات التاريخية الأولية...")
            
            # جلب البيانات لجميع الأزواج النشطة
            candles_data = await self.client.get_multiple_candles(
                list(self.active_pairs),
                trading_config.timeframe_seconds,
                trading_config.timeframe_seconds * trading_config.analysis_candles_count
            )
            
            # حفظ البيانات
            for asset, candles in candles_data.items():
                if candles:
                    await self._save_historical_candles(asset, candles)
                    await self._cache_latest_candles(asset, candles)
            
            successful_pairs = sum(1 for candles in candles_data.values() if len(candles) > 0)
            logger.info(f"📊 تم جمع البيانات التاريخية لـ {successful_pairs}/{len(self.active_pairs)} زوج")
            
        except Exception as e:
            logger.error(f"خطأ في جمع البيانات التاريخية: {e}")
    
    async def _save_historical_candles(self, asset: str, candles: List[Dict[str, Any]]):
        """حفظ الشموع التاريخية في قاعدة البيانات"""
        try:
            from config.database import get_db_session

            # استخدام session متزامن مع repositories
            session = get_db_session()

            # الحصول على زوج العملة أو إنشاؤه
            pair_repo = CurrencyPairRepository(session)
            pair = pair_repo.get_or_create(asset)

            # حفظ الشموع
            candle_repo = CandleRepository(session)
            saved_count = 0

            for candle_data in candles:
                    try:
                        # التحقق من صحة البيانات
                        if not self._validate_candle_data(candle_data):
                            continue
                        
                        timestamp = datetime.fromtimestamp(candle_data['time'])
                        
                        # التحقق من عدم وجود الشمعة مسبقاً
                        existing = candle_repo.get_by_timestamp(pair.id, timestamp, trading_config.timeframe_seconds)
                        if existing:
                            continue
                        
                        candle_repo.create(
                            pair_id=pair.id,
                            timestamp=timestamp,
                            open_price=float(candle_data['open']),
                            high_price=float(candle_data['high']),
                            low_price=float(candle_data['low']),
                            close_price=float(candle_data['close']),
                            volume=float(candle_data.get('volume', 0)),
                            timeframe=trading_config.timeframe_seconds
                        )
                        saved_count += 1
                        
                    except Exception as e:
                        logger.warning(f"تخطي شمعة لـ {asset}: {e}")
                        continue
                
            session.commit()
            logger.info(f"💾 تم حفظ {saved_count} شمعة لـ {asset}")

            # تنظيف الشموع القديمة
            self._cleanup_old_candles(pair.id, candle_repo)

            session.close()
                
        except Exception as e:
            logger.error(f"خطأ في حفظ بيانات {asset}: {e}")
    
    def _validate_candle_data(self, candle_data: Dict[str, Any]) -> bool:
        """التحقق من صحة بيانات الشمعة"""
        required_fields = ['time', 'open', 'high', 'low', 'close']
        
        for field in required_fields:
            if field not in candle_data:
                return False
            
            if field == 'time':
                try:
                    datetime.fromtimestamp(candle_data[field])
                except (ValueError, TypeError):
                    return False
            else:
                try:
                    float(candle_data[field])
                except (ValueError, TypeError):
                    return False
        
        # التحقق من منطقية الأسعار
        try:
            open_price = float(candle_data['open'])
            high_price = float(candle_data['high'])
            low_price = float(candle_data['low'])
            close_price = float(candle_data['close'])
            
            if high_price < max(open_price, close_price) or low_price > min(open_price, close_price):
                return False
                
        except (ValueError, TypeError):
            return False
        
        return True
    
    def _cleanup_old_candles(self, pair_id: int, candle_repo: CandleRepository):
        """تنظيف الشموع القديمة"""
        try:
            deleted_count = candle_repo.delete_old_candles(
                pair_id,
                trading_config.max_candles_per_pair,
                trading_config.timeframe_seconds
            )

            if deleted_count > 0:
                logger.info(f"🗑️ تم حذف {deleted_count} شمعة قديمة")

        except Exception as e:
            logger.error(f"خطأ في تنظيف الشموع القديمة: {e}")
    
    async def _cache_latest_candles(self, asset: str, candles: List[Dict[str, Any]]):
        """تخزين أحدث الشموع في Redis"""
        try:
            if not candles:
                return
            
            # تخزين آخر 50 شمعة في Redis
            latest_candles = candles[-50:] if len(candles) > 50 else candles
            
            cache_key = f"candles:{asset}:{trading_config.timeframe_seconds}"
            cache_data = json.dumps(latest_candles, default=str)
            
            await self.redis_client.setex(cache_key, 3600, cache_data)  # انتهاء صلاحية بعد ساعة
            
            # تخزين آخر شمعة منفصلة للوصول السريع
            latest_candle_key = f"latest_candle:{asset}"
            latest_candle_data = json.dumps(latest_candles[-1], default=str)
            await self.redis_client.setex(latest_candle_key, 300, latest_candle_data)  # 5 دقائق
            
        except Exception as e:
            logger.error(f"خطأ في تخزين البيانات في Redis لـ {asset}: {e}")
    
    async def _start_live_streaming(self):
        """بدء البث المباشر"""
        try:
            logger.info("📡 بدء البث المباشر...")
            
            # بدء البث المباشر مع callback
            success = await self.client.start_live_stream(
                list(self.active_pairs),
                self._on_live_data_received
            )
            
            if success:
                logger.info("✅ تم بدء البث المباشر بنجاح")
            else:
                logger.error("❌ فشل في بدء البث المباشر")
                
        except Exception as e:
            logger.error(f"خطأ في بدء البث المباشر: {e}")
    
    async def _on_live_data_received(self, asset: str, candle_data: Dict[str, Any]):
        """معالج استقبال البيانات المباشرة"""
        try:
            # التحقق من صحة البيانات
            if not self._validate_candle_data(candle_data):
                return

            # تحديث Redis
            await self._cache_latest_candles(asset, [candle_data])

            # إشعار المكونات الأخرى بوصول بيانات جديدة
            await self._notify_new_data(asset, candle_data)

            # تسجيل استلام البيانات المباشرة
            logger.debug(f"📡 تم استلام بيانات مباشرة لـ {asset}: {candle_data.get('time', 'N/A')}")

        except Exception as e:
            logger.error(f"خطأ في معالجة البيانات المباشرة لـ {asset}: {e}")
    
    async def _notify_new_data(self, asset: str, candle_data: Dict[str, Any]):
        """إشعار المكونات الأخرى بوصول بيانات جديدة"""
        try:
            # نشر إشعار في Redis
            notification = {
                'asset': asset,
                'timestamp': candle_data['time'],
                'type': 'new_candle'
            }
            
            await self.redis_client.publish('market_data', json.dumps(notification))
            
        except Exception as e:
            logger.error(f"خطأ في إرسال الإشعار: {e}")
    
    async def _maintenance_loop(self):
        """حلقة الصيانة الدورية"""
        while self.running:
            try:
                await asyncio.sleep(300)  # كل 5 دقائق
                
                # فحص الاتصال
                if not self.client.is_connected():
                    logger.warning("⚠️ انقطع الاتصال - محاولة إعادة الاتصال...")
                    await self.client.reconnect()
                
                # تنظيف Redis
                await self._cleanup_redis_cache()
                
            except Exception as e:
                logger.error(f"خطأ في حلقة الصيانة: {e}")
                await asyncio.sleep(60)
    
    async def _cleanup_redis_cache(self):
        """تنظيف ذاكرة التخزين المؤقت"""
        try:
            # حذف المفاتيح المنتهية الصلاحية
            expired_keys = await self.redis_client.keys("*:expired")
            if expired_keys:
                await self.redis_client.delete(*expired_keys)
                
        except Exception as e:
            logger.error(f"خطأ في تنظيف Redis: {e}")
    
    async def get_latest_candles(self, asset: str, count: int = 50) -> List[Dict[str, Any]]:
        """الحصول على أحدث الشموع من Redis"""
        try:
            cache_key = f"candles:{asset}:{trading_config.timeframe_seconds}"
            cached_data = await self.redis_client.get(cache_key)
            
            if cached_data:
                candles = json.loads(cached_data)
                return candles[-count:] if len(candles) > count else candles
            
            return []
            
        except Exception as e:
            logger.error(f"خطأ في جلب البيانات من Redis لـ {asset}: {e}")
            return []
    
    async def add_pair(self, asset: str) -> bool:
        """إضافة زوج جديد للمراقبة"""
        try:
            if asset in self.active_pairs:
                logger.warning(f"⚠️ الزوج {asset} موجود بالفعل")
                return True
            
            # جلب البيانات التاريخية للزوج الجديد
            candles = await self.client.get_candles(
                asset,
                trading_config.timeframe_seconds,
                trading_config.timeframe_seconds * trading_config.analysis_candles_count
            )
            
            if candles:
                await self._save_historical_candles(asset, candles)
                await self._cache_latest_candles(asset, candles)
                self.active_pairs.add(asset)
                logger.info(f"✅ تم إضافة الزوج {asset}")
                return True
            else:
                logger.error(f"❌ فشل في جلب بيانات {asset}")
                return False
                
        except Exception as e:
            logger.error(f"خطأ في إضافة الزوج {asset}: {e}")
            return False
    
    async def remove_pair(self, asset: str) -> bool:
        """إزالة زوج من المراقبة"""
        try:
            if asset not in self.active_pairs:
                logger.warning(f"⚠️ الزوج {asset} غير موجود")
                return True
            
            self.active_pairs.remove(asset)
            
            # حذف البيانات من Redis
            cache_key = f"candles:{asset}:{trading_config.timeframe_seconds}"
            latest_key = f"latest_candle:{asset}"
            await self.redis_client.delete(cache_key, latest_key)
            
            logger.info(f"✅ تم إزالة الزوج {asset}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في إزالة الزوج {asset}: {e}")
            return False
    
    async def stop_data_collection(self):
        """إيقاف جمع البيانات"""
        try:
            logger.info("🛑 إيقاف جمع البيانات...")
            self.running = False
            
            # إيقاف المهام
            for task in self.data_collection_tasks.values():
                if not task.done():
                    task.cancel()
            
            # قطع الاتصال
            await self.client.disconnect()
            
            logger.info("✅ تم إيقاف جمع البيانات")
            
        except Exception as e:
            logger.error(f"خطأ في إيقاف جمع البيانات: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """الحصول على حالة مدير البيانات"""
        return {
            'running': self.running,
            'connected': self.client.is_connected(),
            'active_pairs': list(self.active_pairs),
            'active_pairs_count': len(self.active_pairs),
            'connection_info': self.client.get_connection_info()
        }
