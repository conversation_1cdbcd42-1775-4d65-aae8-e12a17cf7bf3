2025-06-23 07:49:20 | ERROR    | config.database:test_connections:151 | اختبار PostgreSQL: فشل - Textual SQL expression 'SELECT 1' should be explicitly declared as text('SELECT 1')
2025-06-23 07:50:59 | ERROR    | __main__:test_parallel_historical_data:91 | ❌ EURUSD_otc: Cannot run the event loop while another loop is running
2025-06-23 07:50:59 | ERROR    | __main__:test_parallel_historical_data:91 | ❌ GBPUSD_otc: Cannot run the event loop while another loop is running
2025-06-23 07:50:59 | ERROR    | __main__:test_parallel_historical_data:91 | ❌ USDJPY_otc: Cannot run the event loop while another loop is running
2025-06-23 07:50:59 | ERROR    | __main__:test_parallel_historical_data:91 | ❌ AUDCAD_otc: Cannot run the event loop while another loop is running
2025-06-23 07:50:59 | ERROR    | __main__:test_parallel_historical_data:91 | ❌ CADJPY_otc: Cannot run the event loop while another loop is running
2025-06-23 07:54:22 | ERROR    | __main__:test_parallel_historical_data:91 | ❌ EURUSD_otc: Cannot run the event loop while another loop is running
2025-06-23 07:54:22 | ERROR    | __main__:test_parallel_historical_data:91 | ❌ GBPUSD_otc: Cannot run the event loop while another loop is running
2025-06-23 07:54:22 | ERROR    | __main__:test_parallel_historical_data:91 | ❌ USDJPY_otc: Cannot run the event loop while another loop is running
2025-06-23 07:54:22 | ERROR    | __main__:test_parallel_historical_data:91 | ❌ AUDCAD_otc: Cannot run the event loop while another loop is running
2025-06-23 07:54:22 | ERROR    | __main__:test_parallel_historical_data:91 | ❌ CADJPY_otc: Cannot run the event loop while another loop is running
2025-06-23 07:55:24 | ERROR    | __main__:test_parallel_historical_data:84 | ❌ EURUSD_otc: Cannot run the event loop while another loop is running
2025-06-23 07:55:24 | ERROR    | __main__:test_parallel_historical_data:84 | ❌ GBPUSD_otc: Cannot run the event loop while another loop is running
2025-06-23 07:55:24 | ERROR    | __main__:test_parallel_historical_data:84 | ❌ USDJPY_otc: Cannot run the event loop while another loop is running
2025-06-23 07:55:24 | ERROR    | __main__:test_parallel_historical_data:84 | ❌ AUDCAD_otc: Cannot run the event loop while another loop is running
2025-06-23 07:55:24 | ERROR    | __main__:test_parallel_historical_data:84 | ❌ CADJPY_otc: Cannot run the event loop while another loop is running
2025-06-23 08:04:30 | ERROR    | data_collector.pocket_option_client:reconnect:77 | ❌ تجاوز الحد الأقصى لمحاولات الاتصال (5)
2025-06-23 08:04:30 | ERROR    | data_collector.pocket_option_client:connect:47 | ❌ فشل في اختبار الاتصال
2025-06-23 08:04:30 | ERROR    | data_collector.pocket_option_client:connect:47 | ❌ فشل في اختبار الاتصال
2025-06-23 08:04:30 | ERROR    | data_collector.pocket_option_client:connect:47 | ❌ فشل في اختبار الاتصال
2025-06-23 08:04:30 | ERROR    | data_collector.pocket_option_client:connect:47 | ❌ فشل في اختبار الاتصال
2025-06-23 08:04:30 | ERROR    | data_collector.pocket_option_client:connect:47 | ❌ فشل في اختبار الاتصال
2025-06-23 08:04:30 | ERROR    | data_collector.pocket_option_client:connect:47 | ❌ فشل في اختبار الاتصال
2025-06-23 08:04:30 | ERROR    | data_collector.data_manager:initialize:37 | ❌ فشل في الاتصال بالمنصة
2025-06-23 08:06:59 | ERROR    | data_collector.data_manager:_save_historical_candles:152 | خطأ في حفظ بيانات EURUSD_otc: 'AsyncSession' object has no attribute 'query'
2025-06-23 08:06:59 | ERROR    | data_collector.data_manager:_save_historical_candles:152 | خطأ في حفظ بيانات AUDCAD_otc: 'AsyncSession' object has no attribute 'query'
2025-06-23 08:06:59 | ERROR    | data_collector.data_manager:_save_historical_candles:152 | خطأ في حفظ بيانات GBPUSD_otc: 'AsyncSession' object has no attribute 'query'
2025-06-23 08:06:59 | ERROR    | data_collector.data_manager:_save_historical_candles:152 | خطأ في حفظ بيانات USDJPY_otc: 'AsyncSession' object has no attribute 'query'
2025-06-23 08:06:59 | ERROR    | data_collector.data_manager:_save_historical_candles:152 | خطأ في حفظ بيانات CADJPY_otc: 'AsyncSession' object has no attribute 'query'
