"""
اختبار الاتصال بمنصة Pocket Option
Pocket Option Connection Test
"""
import sys
import time
import asyncio
from pathlib import Path

# إضافة المجلد الحالي إلى مسار Python
sys.path.append(str(Path(__file__).parent))

from BinaryOptionsToolsV2.pocketoption import PocketOption
from config.settings import pocket_option_config, get_current_ssid
from utils.logger import get_logger

logger = get_logger(__name__)

class PocketOptionConnectionTest:
    """فئة اختبار الاتصال بمنصة Pocket Option"""
    
    def __init__(self):
        self.demo_ssid = pocket_option_config.demo_ssid
        self.real_ssid = pocket_option_config.real_ssid
        self.current_ssid = get_current_ssid()
        self.api = None
        
    def test_ssid_format(self, ssid: str, account_type: str) -> bool:
        """اختبار تنسيق SSID"""
        print(f"🔍 اختبار تنسيق SSID للحساب {account_type}...")
        
        try:
            # التحقق من أن SSID يبدأ بـ 42["auth"
            if not ssid.startswith('42["auth"'):
                print(f"   ❌ SSID لا يبدأ بالتنسيق الصحيح")
                return False
            
            # التحقق من أن SSID ينتهي بـ ]
            if not ssid.endswith(']'):
                print(f"   ❌ SSID لا ينتهي بالتنسيق الصحيح")
                return False
            
            # التحقق من وجود session
            if '"session"' not in ssid:
                print(f"   ❌ SSID لا يحتوي على session")
                return False
            
            # التحقق من وجود uid
            if '"uid"' not in ssid:
                print(f"   ❌ SSID لا يحتوي على uid")
                return False
            
            print(f"   ✅ تنسيق SSID صحيح للحساب {account_type}")
            return True
            
        except Exception as e:
            print(f"   ❌ خطأ في فحص SSID: {e}")
            return False
    
    def test_connection(self, ssid: str, account_type: str) -> bool:
        """اختبار الاتصال بالمنصة"""
        print(f"\n🔌 اختبار الاتصال بالحساب {account_type}...")
        
        try:
            # إنشاء عميل الاتصال
            print("   📡 إنشاء عميل الاتصال...")
            self.api = PocketOption(ssid)
            
            # انتظار تأسيس الاتصال
            print("   ⏳ انتظار تأسيس الاتصال (5 ثواني)...")
            time.sleep(5)
            
            print(f"   ✅ تم إنشاء الاتصال بنجاح للحساب {account_type}")
            return True
            
        except Exception as e:
            print(f"   ❌ فشل في الاتصال: {e}")
            return False
    
    def test_balance(self) -> bool:
        """اختبار جلب الرصيد"""
        print("\n💰 اختبار جلب الرصيد...")
        
        try:
            balance = self.api.balance()
            
            if balance is not None:
                print(f"   ✅ الرصيد الحالي: ${balance}")
                return True
            else:
                print("   ❌ فشل في جلب الرصيد")
                return False
                
        except Exception as e:
            print(f"   ❌ خطأ في جلب الرصيد: {e}")
            return False
    
    def test_payouts(self) -> bool:
        """اختبار جلب نسب الأرباح"""
        print("\n📊 اختبار جلب نسب الأرباح...")
        
        try:
            payouts = self.api.payout()
            
            if payouts and len(payouts) > 0:
                print(f"   ✅ تم جلب نسب الأرباح: {len(payouts)} أصل متاح")
                
                # عرض أول 5 أصول
                sorted_payouts = dict(sorted(payouts.items(), key=lambda x: x[1], reverse=True))
                print("   📈 أفضل 5 أصول:")
                for i, (asset, payout) in enumerate(list(sorted_payouts.items())[:5]):
                    print(f"      {i+1}. {asset}: {payout}%")
                
                return True
            else:
                print("   ❌ فشل في جلب نسب الأرباح")
                return False
                
        except Exception as e:
            print(f"   ❌ خطأ في جلب نسب الأرباح: {e}")
            return False
    
    def test_historical_data(self) -> bool:
        """اختبار جلب البيانات التاريخية"""
        print("\n📈 اختبار جلب البيانات التاريخية...")
        
        try:
            # اختبار جلب شموع EURUSD_otc
            test_asset = "EURUSD_otc"
            timeframe = 300  # 5 دقائق
            period = 3600    # ساعة واحدة
            
            print(f"   📊 جلب بيانات {test_asset} (إطار زمني: {timeframe}s, فترة: {period}s)...")
            candles = self.api.get_candles(test_asset, timeframe, period)
            
            if candles and len(candles) > 0:
                print(f"   ✅ تم جلب {len(candles)} شمعة")
                
                # عرض آخر شمعة
                latest = candles[-1]
                print(f"   📊 آخر شمعة:")
                print(f"      الوقت: {latest.get('time', 'غير متاح')}")
                print(f"      الافتتاح: {latest.get('open', 'غير متاح')}")
                print(f"      الإغلاق: {latest.get('close', 'غير متاح')}")
                print(f"      الأعلى: {latest.get('high', 'غير متاح')}")
                print(f"      الأدنى: {latest.get('low', 'غير متاح')}")
                
                return True
            else:
                print("   ❌ فشل في جلب البيانات التاريخية")
                return False
                
        except Exception as e:
            print(f"   ❌ خطأ في جلب البيانات التاريخية: {e}")
            return False
    
    def test_server_time(self) -> bool:
        """اختبار جلب وقت الخادم"""
        print("\n⏰ اختبار جلب وقت الخادم...")
        
        try:
            server_time = self.api.get_server_time()
            
            if server_time:
                print(f"   ✅ وقت الخادم: {server_time}")
                return True
            else:
                print("   ❌ فشل في جلب وقت الخادم")
                return False
                
        except Exception as e:
            print(f"   ❌ خطأ في جلب وقت الخادم: {e}")
            return False
    
    def run_full_test(self, ssid: str, account_type: str) -> dict:
        """تشغيل اختبار شامل"""
        print(f"\n{'='*60}")
        print(f"🧪 بدء اختبار شامل للحساب {account_type}")
        print(f"{'='*60}")
        
        results = {
            'ssid_format': False,
            'connection': False,
            'balance': False,
            'payouts': False,
            'historical_data': False,
            'server_time': False
        }
        
        # اختبار تنسيق SSID
        results['ssid_format'] = self.test_ssid_format(ssid, account_type)
        
        if results['ssid_format']:
            # اختبار الاتصال
            results['connection'] = self.test_connection(ssid, account_type)
            
            if results['connection']:
                # اختبار الوظائف
                results['balance'] = self.test_balance()
                results['payouts'] = self.test_payouts()
                results['historical_data'] = self.test_historical_data()
                results['server_time'] = self.test_server_time()
        
        return results
    
    def print_results(self, results: dict, account_type: str):
        """طباعة النتائج"""
        print(f"\n📊 نتائج اختبار الحساب {account_type}:")
        print("-" * 40)
        
        tests = [
            ('تنسيق SSID', 'ssid_format'),
            ('الاتصال', 'connection'),
            ('الرصيد', 'balance'),
            ('نسب الأرباح', 'payouts'),
            ('البيانات التاريخية', 'historical_data'),
            ('وقت الخادم', 'server_time')
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_key in tests:
            status = "✅ نجح" if results[test_key] else "❌ فشل"
            print(f"{test_name}: {status}")
            if results[test_key]:
                passed += 1
        
        print(f"\nالنتيجة الإجمالية: {passed}/{total}")
        
        if passed == total:
            print(f"🎉 جميع الاختبارات نجحت للحساب {account_type}!")
        elif passed >= total * 0.8:
            print(f"⚠️ معظم الاختبارات نجحت للحساب {account_type}")
        else:
            print(f"❌ فشل في عدة اختبارات للحساب {account_type}")
        
        return passed / total

def main():
    """الدالة الرئيسية"""
    print("🧪 بدء اختبار الاتصال بمنصة Pocket Option")
    print("=" * 60)
    
    tester = PocketOptionConnectionTest()
    
    # اختبار الحساب الحالي (حسب الإعدادات)
    current_account_type = "تجريبي" if pocket_option_config.use_demo_account else "حقيقي"
    current_ssid = tester.current_ssid
    
    print(f"🎯 الحساب المحدد حالياً: {current_account_type}")
    
    # تشغيل الاختبار
    results = tester.run_full_test(current_ssid, current_account_type)
    success_rate = tester.print_results(results, current_account_type)
    
    print("\n" + "=" * 60)
    
    if success_rate >= 0.8:
        print("🎉 الاتصال بمنصة Pocket Option يعمل بشكل جيد!")
        print("✅ يمكن المتابعة إلى المرحلة التالية")
        return True
    else:
        print("❌ يوجد مشاكل في الاتصال بمنصة Pocket Option")
        print("🔧 يجب حل المشاكل قبل المتابعة")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
