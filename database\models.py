"""
نماذج قاعدة البيانات
Database Models
"""
from datetime import datetime
from typing import Optional, Dict, Any
from sqlalchemy import (
    Column, Integer, String, DateTime, Boolean,
    Text, ForeignKey, Index, UniqueConstraint, JSON, Numeric
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

Base = declarative_base()

class CurrencyPair(Base):
    """نموذج أزواج العملات"""
    __tablename__ = 'currency_pairs'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String(20), unique=True, nullable=False, index=True)
    name = Column(String(100))
    is_active = Column(Boolean, default=True, nullable=False)
    created_at = Column(DateTime, default=func.now(), nullable=False)
    
    # العلاقات
    candles = relationship("Candle", back_populates="pair", cascade="all, delete-orphan")
    trades = relationship("Trade", back_populates="pair", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<CurrencyPair(symbol='{self.symbol}', name='{self.name}')>"

class Candle(Base):
    """نموذج الشموع"""
    __tablename__ = 'candles'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    pair_id = Column(Integer, ForeignKey('currency_pairs.id'), nullable=False)
    timestamp = Column(DateTime, nullable=False, index=True)
    open_price = Column(Numeric(10, 5), nullable=False)
    high_price = Column(Numeric(10, 5), nullable=False)
    low_price = Column(Numeric(10, 5), nullable=False)
    close_price = Column(Numeric(10, 5), nullable=False)
    volume = Column(Numeric(15, 2), default=0)
    timeframe = Column(Integer, default=300, nullable=False)  # 5 minutes
    created_at = Column(DateTime, default=func.now(), nullable=False)
    
    # العلاقات
    pair = relationship("CurrencyPair", back_populates="candles")
    indicators = relationship("TechnicalIndicator", back_populates="candle", cascade="all, delete-orphan")
    
    # فهارس
    __table_args__ = (
        UniqueConstraint('pair_id', 'timestamp', 'timeframe', name='uq_candle_pair_time'),
        Index('idx_candles_pair_timestamp', 'pair_id', 'timestamp'),
        Index('idx_candles_timestamp', 'timestamp'),
    )
    
    def __repr__(self):
        return f"<Candle(pair_id={self.pair_id}, timestamp='{self.timestamp}', close={self.close_price})>"
    
    def to_dict(self) -> Dict[str, Any]:
        """تحويل الشمعة إلى قاموس"""
        return {
            'id': self.id,
            'pair_id': self.pair_id,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,
            'open': float(self.open_price),
            'high': float(self.high_price),
            'low': float(self.low_price),
            'close': float(self.close_price),
            'volume': float(self.volume) if self.volume else 0,
            'timeframe': self.timeframe
        }

class TechnicalIndicator(Base):
    """نموذج المؤشرات الفنية"""
    __tablename__ = 'technical_indicators'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    candle_id = Column(Integer, ForeignKey('candles.id'), nullable=False)
    indicator_name = Column(String(50), nullable=False, index=True)
    value = Column(Numeric(15, 8))
    extra_data = Column(JSON)  # معلومات إضافية للمؤشر
    calculated_at = Column(DateTime, default=func.now(), nullable=False)
    
    # العلاقات
    candle = relationship("Candle", back_populates="indicators")
    
    # فهارس
    __table_args__ = (
        Index('idx_technical_indicators_candle', 'candle_id'),
        Index('idx_technical_indicators_name', 'indicator_name'),
    )
    
    def __repr__(self):
        return f"<TechnicalIndicator(candle_id={self.candle_id}, name='{self.indicator_name}', value={self.value})>"

class Trade(Base):
    """نموذج الصفقات"""
    __tablename__ = 'trades'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    pair_id = Column(Integer, ForeignKey('currency_pairs.id'), nullable=False)
    trade_type = Column(String(10), nullable=False)  # CALL/PUT
    amount = Column(Numeric(10, 2), nullable=False)
    entry_price = Column(Numeric(10, 5), nullable=False)
    entry_time = Column(DateTime, nullable=False, index=True)
    expiry_time = Column(DateTime, nullable=False)
    exit_price = Column(Numeric(10, 5))
    result = Column(String(10))  # WIN/LOSS/DRAW
    profit_loss = Column(Numeric(10, 2))
    confidence_score = Column(Numeric(5, 2))  # نسبة الثقة
    
    # إشارات التحليل
    technical_signals = Column(JSON)  # إشارات التحليل الفني
    behavioral_signals = Column(JSON)  # إشارات التحليل السلوكي
    ai_prediction = Column(JSON)  # توقعات الذكاء الاصطناعي
    
    # معلومات إضافية
    platform_trade_id = Column(String(50))  # معرف الصفقة في المنصة
    notes = Column(Text)  # ملاحظات
    created_at = Column(DateTime, default=func.now(), nullable=False)
    
    # العلاقات
    pair = relationship("CurrencyPair", back_populates="trades")
    
    # فهارس
    __table_args__ = (
        Index('idx_trades_pair_time', 'pair_id', 'entry_time'),
        Index('idx_trades_result', 'result'),
        Index('idx_trades_type', 'trade_type'),
    )
    
    def __repr__(self):
        return f"<Trade(pair_id={self.pair_id}, type='{self.trade_type}', amount={self.amount}, result='{self.result}')>"
    
    def to_dict(self) -> Dict[str, Any]:
        """تحويل الصفقة إلى قاموس"""
        return {
            'id': self.id,
            'pair_id': self.pair_id,
            'trade_type': self.trade_type,
            'amount': float(self.amount),
            'entry_price': float(self.entry_price),
            'entry_time': self.entry_time.isoformat() if self.entry_time else None,
            'expiry_time': self.expiry_time.isoformat() if self.expiry_time else None,
            'exit_price': float(self.exit_price) if self.exit_price else None,
            'result': self.result,
            'profit_loss': float(self.profit_loss) if self.profit_loss else None,
            'confidence_score': float(self.confidence_score) if self.confidence_score else None,
            'technical_signals': self.technical_signals,
            'behavioral_signals': self.behavioral_signals,
            'ai_prediction': self.ai_prediction,
            'platform_trade_id': self.platform_trade_id,
            'notes': self.notes,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class RiskSetting(Base):
    """نموذج إعدادات إدارة المخاطر"""
    __tablename__ = 'risk_settings'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    setting_name = Column(String(50), unique=True, nullable=False, index=True)
    setting_value = Column(JSON, nullable=False)
    description = Column(Text)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    created_at = Column(DateTime, default=func.now(), nullable=False)
    
    def __repr__(self):
        return f"<RiskSetting(name='{self.setting_name}', value={self.setting_value})>"

class SystemLog(Base):
    """نموذج سجلات النظام"""
    __tablename__ = 'system_logs'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    level = Column(String(20), nullable=False, index=True)  # INFO, WARNING, ERROR, etc.
    component = Column(String(50), nullable=False, index=True)  # DATA_COLLECTOR, AI_PREDICTOR, etc.
    message = Column(Text, nullable=False)
    details = Column(JSON)  # تفاصيل إضافية
    timestamp = Column(DateTime, default=func.now(), nullable=False, index=True)
    
    # فهارس
    __table_args__ = (
        Index('idx_system_logs_level_time', 'level', 'timestamp'),
        Index('idx_system_logs_component_time', 'component', 'timestamp'),
    )
    
    def __repr__(self):
        return f"<SystemLog(level='{self.level}', component='{self.component}', timestamp='{self.timestamp}')>"

class PerformanceMetric(Base):
    """نموذج مقاييس الأداء"""
    __tablename__ = 'performance_metrics'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    metric_name = Column(String(50), nullable=False, index=True)
    metric_value = Column(Numeric(15, 8), nullable=False)
    metric_unit = Column(String(20))  # seconds, percentage, count, etc.
    component = Column(String(50), nullable=False, index=True)
    timestamp = Column(DateTime, default=func.now(), nullable=False, index=True)
    
    # فهارس
    __table_args__ = (
        Index('idx_performance_metrics_name_time', 'metric_name', 'timestamp'),
        Index('idx_performance_metrics_component_time', 'component', 'timestamp'),
    )
    
    def __repr__(self):
        return f"<PerformanceMetric(name='{self.metric_name}', value={self.metric_value}, component='{self.component}')>"
