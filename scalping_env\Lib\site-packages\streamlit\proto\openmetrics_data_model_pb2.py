# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: streamlit/proto/openmetrics_data_model.proto
# Protobuf Python Version: 5.26.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n,streamlit/proto/openmetrics_data_model.proto\x12\x0bopenmetrics\x1a\x1fgoogle/protobuf/timestamp.proto\"?\n\tMetricSet\x12\x32\n\x0fmetric_families\x18\x01 \x03(\x0b\x32\x19.openmetrics.MetricFamily\"\x85\x01\n\x0cMetricFamily\x12\x0c\n\x04name\x18\x01 \x01(\t\x12%\n\x04type\x18\x02 \x01(\x0e\x32\x17.openmetrics.MetricType\x12\x0c\n\x04unit\x18\x03 \x01(\t\x12\x0c\n\x04help\x18\x04 \x01(\t\x12$\n\x07metrics\x18\x05 \x03(\x0b\x32\x13.openmetrics.Metric\"]\n\x06Metric\x12\"\n\x06labels\x18\x01 \x03(\x0b\x32\x12.openmetrics.Label\x12/\n\rmetric_points\x18\x02 \x03(\x0b\x32\x18.openmetrics.MetricPoint\"$\n\x05Label\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\"\xae\x03\n\x0bMetricPoint\x12\x32\n\runknown_value\x18\x01 \x01(\x0b\x32\x19.openmetrics.UnknownValueH\x00\x12.\n\x0bgauge_value\x18\x02 \x01(\x0b\x32\x17.openmetrics.GaugeValueH\x00\x12\x32\n\rcounter_value\x18\x03 \x01(\x0b\x32\x19.openmetrics.CounterValueH\x00\x12\x36\n\x0fhistogram_value\x18\x04 \x01(\x0b\x32\x1b.openmetrics.HistogramValueH\x00\x12\x35\n\x0fstate_set_value\x18\x05 \x01(\x0b\x32\x1a.openmetrics.StateSetValueH\x00\x12,\n\ninfo_value\x18\x06 \x01(\x0b\x32\x16.openmetrics.InfoValueH\x00\x12\x32\n\rsummary_value\x18\x07 \x01(\x0b\x32\x19.openmetrics.SummaryValueH\x00\x12-\n\ttimestamp\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.TimestampB\x07\n\x05value\"D\n\x0cUnknownValue\x12\x16\n\x0c\x64ouble_value\x18\x01 \x01(\x01H\x00\x12\x13\n\tint_value\x18\x02 \x01(\x03H\x00\x42\x07\n\x05value\"B\n\nGaugeValue\x12\x16\n\x0c\x64ouble_value\x18\x01 \x01(\x01H\x00\x12\x13\n\tint_value\x18\x02 \x01(\x03H\x00\x42\x07\n\x05value\"\x9a\x01\n\x0c\x43ounterValue\x12\x16\n\x0c\x64ouble_value\x18\x01 \x01(\x01H\x00\x12\x13\n\tint_value\x18\x02 \x01(\x04H\x00\x12+\n\x07\x63reated\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\'\n\x08\x65xemplar\x18\x04 \x01(\x0b\x32\x15.openmetrics.ExemplarB\x07\n\x05total\"\x8c\x02\n\x0eHistogramValue\x12\x16\n\x0c\x64ouble_value\x18\x01 \x01(\x01H\x00\x12\x13\n\tint_value\x18\x02 \x01(\x03H\x00\x12\r\n\x05\x63ount\x18\x03 \x01(\x04\x12+\n\x07\x63reated\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x33\n\x07\x62uckets\x18\x05 \x03(\x0b\x32\".openmetrics.HistogramValue.Bucket\x1aU\n\x06\x42ucket\x12\r\n\x05\x63ount\x18\x01 \x01(\x04\x12\x13\n\x0bupper_bound\x18\x02 \x01(\x01\x12\'\n\x08\x65xemplar\x18\x03 \x01(\x0b\x32\x15.openmetrics.ExemplarB\x05\n\x03sum\"k\n\x08\x45xemplar\x12\r\n\x05value\x18\x01 \x01(\x01\x12-\n\ttimestamp\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12!\n\x05label\x18\x03 \x03(\x0b\x32\x12.openmetrics.Label\"i\n\rStateSetValue\x12\x30\n\x06states\x18\x01 \x03(\x0b\x32 .openmetrics.StateSetValue.State\x1a&\n\x05State\x12\x0f\n\x07\x65nabled\x18\x01 \x01(\x08\x12\x0c\n\x04name\x18\x02 \x01(\t\"-\n\tInfoValue\x12 \n\x04info\x18\x01 \x03(\x0b\x32\x12.openmetrics.Label\"\xe1\x01\n\x0cSummaryValue\x12\x16\n\x0c\x64ouble_value\x18\x01 \x01(\x01H\x00\x12\x13\n\tint_value\x18\x02 \x01(\x03H\x00\x12\r\n\x05\x63ount\x18\x03 \x01(\x04\x12+\n\x07\x63reated\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x34\n\x08quantile\x18\x05 \x03(\x0b\x32\".openmetrics.SummaryValue.Quantile\x1a+\n\x08Quantile\x12\x10\n\x08quantile\x18\x01 \x01(\x01\x12\r\n\x05value\x18\x02 \x01(\x01\x42\x05\n\x03sum*{\n\nMetricType\x12\x0b\n\x07UNKNOWN\x10\x00\x12\t\n\x05GAUGE\x10\x01\x12\x0b\n\x07\x43OUNTER\x10\x02\x12\r\n\tSTATE_SET\x10\x03\x12\x08\n\x04INFO\x10\x04\x12\r\n\tHISTOGRAM\x10\x05\x12\x13\n\x0fGAUGE_HISTOGRAM\x10\x06\x12\x0b\n\x07SUMMARY\x10\x07\x42\x39\n\x1c\x63om.snowflake.apps.streamlitB\x19OpenmetricsDataModelProtob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'streamlit.proto.openmetrics_data_model_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\034com.snowflake.apps.streamlitB\031OpenmetricsDataModelProto'
  _globals['_METRICTYPE']._serialized_start=1918
  _globals['_METRICTYPE']._serialized_end=2041
  _globals['_METRICSET']._serialized_start=94
  _globals['_METRICSET']._serialized_end=157
  _globals['_METRICFAMILY']._serialized_start=160
  _globals['_METRICFAMILY']._serialized_end=293
  _globals['_METRIC']._serialized_start=295
  _globals['_METRIC']._serialized_end=388
  _globals['_LABEL']._serialized_start=390
  _globals['_LABEL']._serialized_end=426
  _globals['_METRICPOINT']._serialized_start=429
  _globals['_METRICPOINT']._serialized_end=859
  _globals['_UNKNOWNVALUE']._serialized_start=861
  _globals['_UNKNOWNVALUE']._serialized_end=929
  _globals['_GAUGEVALUE']._serialized_start=931
  _globals['_GAUGEVALUE']._serialized_end=997
  _globals['_COUNTERVALUE']._serialized_start=1000
  _globals['_COUNTERVALUE']._serialized_end=1154
  _globals['_HISTOGRAMVALUE']._serialized_start=1157
  _globals['_HISTOGRAMVALUE']._serialized_end=1425
  _globals['_HISTOGRAMVALUE_BUCKET']._serialized_start=1333
  _globals['_HISTOGRAMVALUE_BUCKET']._serialized_end=1418
  _globals['_EXEMPLAR']._serialized_start=1427
  _globals['_EXEMPLAR']._serialized_end=1534
  _globals['_STATESETVALUE']._serialized_start=1536
  _globals['_STATESETVALUE']._serialized_end=1641
  _globals['_STATESETVALUE_STATE']._serialized_start=1603
  _globals['_STATESETVALUE_STATE']._serialized_end=1641
  _globals['_INFOVALUE']._serialized_start=1643
  _globals['_INFOVALUE']._serialized_end=1688
  _globals['_SUMMARYVALUE']._serialized_start=1691
  _globals['_SUMMARYVALUE']._serialized_end=1916
  _globals['_SUMMARYVALUE_QUANTILE']._serialized_start=1866
  _globals['_SUMMARYVALUE_QUANTILE']._serialized_end=1909
# @@protoc_insertion_point(module_scope)
