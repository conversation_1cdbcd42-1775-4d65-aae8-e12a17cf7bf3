"""
إعداد قاعدة البيانات
Database Setup
"""
import sys
import asyncio
from pathlib import Path

# إضافة المجلد الحالي إلى مسار Python
sys.path.append(str(Path(__file__).parent))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import OperationalError
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

from config.settings import db_config, get_database_url
from config.database import initialize_databases, test_database_connections
from database.models import Base
from utils.logger import get_logger

logger = get_logger(__name__)

class DatabaseSetup:
    """فئة إعداد قاعدة البيانات"""
    
    def __init__(self):
        self.db_config = db_config
        
    def create_database_if_not_exists(self):
        """إنشاء قاعدة البيانات إذا لم تكن موجودة"""
        print("🗄️ التحقق من وجود قاعدة البيانات...")
        
        try:
            # الاتصال بقاعدة بيانات postgres الافتراضية
            conn = psycopg2.connect(
                host=self.db_config.postgres_host,
                port=self.db_config.postgres_port,
                user=self.db_config.postgres_user,
                password=self.db_config.postgres_password,
                database='postgres'
            )
            conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
            cursor = conn.cursor()
            
            # التحقق من وجود قاعدة البيانات
            cursor.execute(
                "SELECT 1 FROM pg_catalog.pg_database WHERE datname = %s",
                (self.db_config.postgres_db,)
            )
            
            if cursor.fetchone():
                print(f"   ✅ قاعدة البيانات '{self.db_config.postgres_db}' موجودة")
            else:
                print(f"   📝 إنشاء قاعدة البيانات '{self.db_config.postgres_db}'...")
                cursor.execute(f'CREATE DATABASE "{self.db_config.postgres_db}"')
                print(f"   ✅ تم إنشاء قاعدة البيانات '{self.db_config.postgres_db}' بنجاح")
            
            cursor.close()
            conn.close()
            return True
            
        except psycopg2.Error as e:
            print(f"   ❌ خطأ في PostgreSQL: {e}")
            return False
        except Exception as e:
            print(f"   ❌ خطأ غير متوقع: {e}")
            return False
    
    def create_tables(self):
        """إنشاء الجداول"""
        print("\n📊 إنشاء الجداول...")
        
        try:
            # إنشاء محرك قاعدة البيانات
            engine = create_engine(get_database_url())
            
            # إنشاء جميع الجداول
            Base.metadata.create_all(engine)
            
            print("   ✅ تم إنشاء جميع الجداول بنجاح")
            
            # عرض الجداول المنشأة
            with engine.connect() as conn:
                result = conn.execute(text("""
                    SELECT table_name 
                    FROM information_schema.tables 
                    WHERE table_schema = 'public'
                    ORDER BY table_name
                """))
                
                tables = [row[0] for row in result]
                print(f"   📋 الجداول المنشأة ({len(tables)}):")
                for table in tables:
                    print(f"      - {table}")
            
            engine.dispose()
            return True
            
        except Exception as e:
            print(f"   ❌ خطأ في إنشاء الجداول: {e}")
            return False
    
    def insert_initial_data(self):
        """إدراج البيانات الأولية"""
        print("\n📝 إدراج البيانات الأولية...")
        
        try:
            from database.repositories import CurrencyPairRepository, RiskSettingRepository
            from config.settings import INITIAL_TEST_PAIRS
            from config.constants import RiskConstants


            # إنشاء جلسة مباشرة
            engine = create_engine(get_database_url())
            Session = sessionmaker(bind=engine)
            session = Session()
            
            # إدراج أزواج العملات للاختبار الأولي
            pair_repo = CurrencyPairRepository(session)
            
            print("   💱 إدراج أزواج العملات للاختبار...")
            for symbol in INITIAL_TEST_PAIRS:
                pair = pair_repo.get_or_create(symbol, symbol.replace('_otc', ''))
                print(f"      ✅ {symbol}")
            
            # إدراج إعدادات إدارة المخاطر الافتراضية
            risk_repo = RiskSettingRepository(session)
            
            print("   ⚙️ إدراج إعدادات إدارة المخاطر الافتراضية...")
            
            default_settings = [
                {
                    'name': 'amount_type',
                    'value': {'type': RiskConstants.AMOUNT_TYPE_FIXED},
                    'description': 'نوع إدارة المبلغ: fixed أو percentage'
                },
                {
                    'name': 'fixed_amount',
                    'value': {'amount': RiskConstants.DEFAULT_FIXED_AMOUNT},
                    'description': 'المبلغ الثابت للصفقة'
                },
                {
                    'name': 'percentage_amount',
                    'value': {'percentage': RiskConstants.DEFAULT_PERCENTAGE},
                    'description': 'نسبة من الرصيد للصفقة'
                },
                {
                    'name': 'max_daily_loss',
                    'value': {'amount': RiskConstants.DEFAULT_MAX_DAILY_LOSS},
                    'description': 'الحد الأقصى للخسارة اليومية'
                },
                {
                    'name': 'max_daily_profit',
                    'value': {'amount': RiskConstants.DEFAULT_MAX_DAILY_PROFIT},
                    'description': 'الحد الأقصى للربح اليومي'
                },
                {
                    'name': 'max_consecutive_losses',
                    'value': {'count': RiskConstants.DEFAULT_MAX_CONSECUTIVE_LOSSES},
                    'description': 'عدد الصفقات المتتالية الخاسرة قبل التوقف'
                }
            ]
            
            for setting in default_settings:
                risk_repo.set_setting(
                    setting['name'],
                    setting['value'],
                    setting['description']
                )
                print(f"      ✅ {setting['name']}")
            
            session.close()
            print("   ✅ تم إدراج البيانات الأولية بنجاح")
            return True
            
        except Exception as e:
            print(f"   ❌ خطأ في إدراج البيانات الأولية: {e}")
            return False
    
    async def test_connections(self):
        """اختبار الاتصالات"""
        print("\n🔍 اختبار اتصالات قواعد البيانات...")
        
        try:
            await initialize_databases()
            results = await test_database_connections()
            
            print("   📊 نتائج الاختبار:")
            for db_name, status in results.items():
                status_text = "✅ متصل" if status else "❌ غير متصل"
                print(f"      {db_name}: {status_text}")
            
            return all(results.values())
            
        except Exception as e:
            print(f"   ❌ خطأ في اختبار الاتصالات: {e}")
            return False

async def main():
    """الدالة الرئيسية"""
    print("🚀 بدء إعداد قاعدة البيانات")
    print("=" * 50)
    
    setup = DatabaseSetup()
    
    # خطوات الإعداد
    steps = [
        ("إنشاء قاعدة البيانات", setup.create_database_if_not_exists),
        ("إنشاء الجداول", setup.create_tables),
        ("إدراج البيانات الأولية", setup.insert_initial_data),
        ("اختبار الاتصالات", setup.test_connections)
    ]
    
    success_count = 0
    total_steps = len(steps)
    
    for step_name, step_func in steps:
        try:
            print(f"\n📋 {step_name}...")
            if asyncio.iscoroutinefunction(step_func):
                result = await step_func()
            else:
                result = step_func()
            
            if result:
                success_count += 1
                print(f"✅ {step_name} - نجح")
            else:
                print(f"❌ {step_name} - فشل")
                break
                
        except Exception as e:
            print(f"❌ خطأ في {step_name}: {e}")
            break
    
    print("\n" + "=" * 50)
    print(f"📊 النتيجة النهائية: {success_count}/{total_steps}")
    
    if success_count == total_steps:
        print("🎉 تم إعداد قاعدة البيانات بنجاح!")
        print("✅ النظام جاهز للاستخدام")
        return True
    else:
        print("❌ فشل في إعداد قاعدة البيانات")
        print("🔧 يجب حل المشاكل قبل المتابعة")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الإعداد بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
