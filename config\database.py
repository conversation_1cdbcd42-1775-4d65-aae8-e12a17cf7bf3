"""
إعدادات قاعدة البيانات
Database Configuration
"""
import asyncio
from typing import Optional
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import sessionmaker, Session
import redis.asyncio as aioredis
import redis
from .settings import db_config, get_database_url, get_redis_url
from utils.logger import get_logger

logger = get_logger(__name__)

class DatabaseManager:
    """مدير قواعد البيانات"""
    
    def __init__(self):
        self.postgres_engine = None
        self.async_postgres_engine = None
        self.redis_client = None
        self.async_redis_client = None
        self.session_factory = None
        self.async_session_factory = None
        
    async def initialize(self):
        """تهيئة جميع اتصالات قواعد البيانات"""
        try:
            await self._setup_postgresql()
            await self._setup_redis()
            logger.info("تم تهيئة قواعد البيانات بنجاح")
        except Exception as e:
            logger.error(f"خطأ في تهيئة قواعد البيانات: {e}")
            raise
    
    async def _setup_postgresql(self):
        """إعداد PostgreSQL"""
        try:
            # محرك متزامن
            database_url = get_database_url()
            self.postgres_engine = create_engine(
                database_url,
                pool_size=10,
                max_overflow=20,
                pool_pre_ping=True,
                echo=False
            )
            
            # محرك غير متزامن
            async_database_url = database_url.replace('postgresql://', 'postgresql+asyncpg://')
            self.async_postgres_engine = create_async_engine(
                async_database_url,
                pool_size=10,
                max_overflow=20,
                pool_pre_ping=True,
                echo=False
            )
            
            # مصانع الجلسات
            self.session_factory = sessionmaker(
                bind=self.postgres_engine,
                autocommit=False,
                autoflush=False
            )
            
            self.async_session_factory = async_sessionmaker(
                bind=self.async_postgres_engine,
                class_=AsyncSession,
                autocommit=False,
                autoflush=False
            )
            
            logger.info("تم إعداد PostgreSQL بنجاح")
            
        except Exception as e:
            logger.error(f"خطأ في إعداد PostgreSQL: {e}")
            raise
    
    async def _setup_redis(self):
        """إعداد Redis"""
        try:
            redis_url = get_redis_url()
            
            # عميل متزامن
            self.redis_client = redis.from_url(
                redis_url,
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True
            )
            
            # عميل غير متزامن
            self.async_redis_client = aioredis.from_url(
                redis_url,
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True
            )
            
            # اختبار الاتصال
            await self.async_redis_client.ping()
            logger.info("تم إعداد Redis بنجاح")
            
        except Exception as e:
            logger.error(f"خطأ في إعداد Redis: {e}")
            raise
    
    def get_sync_session(self) -> Session:
        """الحصول على جلسة PostgreSQL متزامنة"""
        if not self.session_factory:
            raise RuntimeError("PostgreSQL غير مهيأ")
        return self.session_factory()
    
    def get_async_session(self) -> AsyncSession:
        """الحصول على جلسة PostgreSQL غير متزامنة"""
        if not self.async_session_factory:
            raise RuntimeError("PostgreSQL غير مهيأ")
        return self.async_session_factory()
    
    def get_redis(self) -> redis.Redis:
        """الحصول على عميل Redis متزامن"""
        if not self.redis_client:
            raise RuntimeError("Redis غير مهيأ")
        return self.redis_client
    
    def get_async_redis(self) -> aioredis.Redis:
        """الحصول على عميل Redis غير متزامن"""
        if not self.async_redis_client:
            raise RuntimeError("Redis غير مهيأ")
        return self.async_redis_client
    
    async def test_connections(self) -> dict:
        """اختبار جميع الاتصالات"""
        results = {
            'postgresql': False,
            'redis': False
        }
        
        # اختبار PostgreSQL
        try:
            from sqlalchemy import text
            async with self.get_async_session() as session:
                result = await session.execute(text("SELECT 1"))
                if result.scalar() == 1:
                    results['postgresql'] = True
                    logger.info("اختبار PostgreSQL: نجح")
        except Exception as e:
            logger.error(f"اختبار PostgreSQL: فشل - {e}")
        
        # اختبار Redis
        try:
            pong = await self.async_redis_client.ping()
            if pong:
                results['redis'] = True
                logger.info("اختبار Redis: نجح")
        except Exception as e:
            logger.error(f"اختبار Redis: فشل - {e}")
        
        return results
    
    async def close(self):
        """إغلاق جميع الاتصالات"""
        try:
            if self.async_postgres_engine:
                await self.async_postgres_engine.dispose()
            
            if self.postgres_engine:
                self.postgres_engine.dispose()
            
            if self.async_redis_client:
                await self.async_redis_client.close()
            
            if self.redis_client:
                self.redis_client.close()
            
            logger.info("تم إغلاق جميع اتصالات قواعد البيانات")
            
        except Exception as e:
            logger.error(f"خطأ في إغلاق اتصالات قواعد البيانات: {e}")

# إنشاء مثيل مدير قواعد البيانات
db_manager = DatabaseManager()

# دوال مساعدة للوصول السريع
def get_db_session() -> Session:
    """الحصول على جلسة قاعدة بيانات متزامنة"""
    return db_manager.get_sync_session()

def get_async_db_session() -> AsyncSession:
    """الحصول على جلسة قاعدة بيانات غير متزامنة"""
    return db_manager.get_async_session()

def get_redis_client() -> redis.Redis:
    """الحصول على عميل Redis متزامن"""
    return db_manager.get_redis()

def get_async_redis_client() -> aioredis.Redis:
    """الحصول على عميل Redis غير متزامن"""
    return db_manager.get_async_redis()

async def initialize_databases():
    """تهيئة جميع قواعد البيانات"""
    await db_manager.initialize()

async def test_database_connections():
    """اختبار جميع اتصالات قواعد البيانات"""
    return await db_manager.test_connections()

async def close_database_connections():
    """إغلاق جميع اتصالات قواعد البيانات"""
    await db_manager.close()
