2025-06-23 07:42:01 | INFO     | utils.logger:initialize:112 | تم تهيئة نظام السجلات بنجاح
2025-06-23 07:43:24 | INFO     | utils.logger:initialize:112 | تم تهيئة نظام السجلات بنجاح
2025-06-23 07:44:29 | INFO     | utils.logger:initialize:112 | تم تهيئة نظام السجلات بنجاح
2025-06-23 07:47:17 | INFO     | utils.logger:initialize:112 | تم تهيئة نظام السجلات بنجاح
2025-06-23 07:48:08 | INFO     | utils.logger:initialize:112 | تم تهيئة نظام السجلات بنجاح
2025-06-23 07:48:38 | INFO     | utils.logger:initialize:112 | تم تهيئة نظام السجلات بنجاح
2025-06-23 07:49:17 | INFO     | utils.logger:initialize:112 | تم تهيئة نظام السجلات بنجاح
2025-06-23 07:49:18 | INFO     | config.database:_setup_postgresql:75 | تم إعداد PostgreSQL بنجاح
2025-06-23 07:49:20 | INFO     | config.database:_setup_redis:106 | تم إعداد Redis بنجاح
2025-06-23 07:49:20 | INFO     | config.database:initialize:33 | تم تهيئة قواعد البيانات بنجاح
2025-06-23 07:49:20 | ERROR    | config.database:test_connections:151 | اختبار PostgreSQL: فشل - Textual SQL expression 'SELECT 1' should be explicitly declared as text('SELECT 1')
2025-06-23 07:49:20 | INFO     | config.database:test_connections:158 | اختبار Redis: نجح
2025-06-23 07:49:38 | INFO     | utils.logger:initialize:112 | تم تهيئة نظام السجلات بنجاح
2025-06-23 07:49:39 | INFO     | config.database:_setup_postgresql:75 | تم إعداد PostgreSQL بنجاح
2025-06-23 07:49:41 | INFO     | config.database:_setup_redis:106 | تم إعداد Redis بنجاح
2025-06-23 07:49:41 | INFO     | config.database:initialize:33 | تم تهيئة قواعد البيانات بنجاح
2025-06-23 07:49:41 | INFO     | config.database:test_connections:150 | اختبار PostgreSQL: نجح
2025-06-23 07:49:41 | INFO     | config.database:test_connections:159 | اختبار Redis: نجح
2025-06-23 07:50:50 | INFO     | utils.logger:initialize:112 | تم تهيئة نظام السجلات بنجاح
2025-06-23 07:50:50 | INFO     | __main__:initialize:35 | 🚀 تهيئة اختبار جمع البيانات المتوازي
2025-06-23 07:50:50 | INFO     | config.database:_setup_postgresql:75 | تم إعداد PostgreSQL بنجاح
2025-06-23 07:50:52 | INFO     | config.database:_setup_redis:106 | تم إعداد Redis بنجاح
2025-06-23 07:50:52 | INFO     | config.database:initialize:33 | تم تهيئة قواعد البيانات بنجاح
2025-06-23 07:50:59 | INFO     | __main__:initialize:46 | ✅ تم تهيئة النظام بنجاح
2025-06-23 07:50:59 | INFO     | __main__:test_parallel_historical_data:55 | 📊 بدء اختبار جمع البيانات التاريخية المتوازي
2025-06-23 07:50:59 | INFO     | __main__:test_parallel_historical_data:75 | ⏳ جمع البيانات لـ 5 أزواج بشكل متوازي...
2025-06-23 07:50:59 | INFO     | utils.logger:log_data_collection:168 | جمع البيانات - الزوج: EURUSD_otc, عدد الشموع: 0, الحالة: فشل - Cannot run the event loop while another loop is running
2025-06-23 07:50:59 | INFO     | utils.logger:log_data_collection:168 | جمع البيانات - الزوج: GBPUSD_otc, عدد الشموع: 0, الحالة: فشل - Cannot run the event loop while another loop is running
2025-06-23 07:50:59 | INFO     | utils.logger:log_data_collection:168 | جمع البيانات - الزوج: USDJPY_otc, عدد الشموع: 0, الحالة: فشل - Cannot run the event loop while another loop is running
2025-06-23 07:50:59 | INFO     | utils.logger:log_data_collection:168 | جمع البيانات - الزوج: AUDCAD_otc, عدد الشموع: 0, الحالة: فشل - Cannot run the event loop while another loop is running
2025-06-23 07:50:59 | INFO     | utils.logger:log_data_collection:168 | جمع البيانات - الزوج: CADJPY_otc, عدد الشموع: 0, الحالة: فشل - Cannot run the event loop while another loop is running
2025-06-23 07:50:59 | ERROR    | __main__:test_parallel_historical_data:91 | ❌ EURUSD_otc: Cannot run the event loop while another loop is running
2025-06-23 07:50:59 | ERROR    | __main__:test_parallel_historical_data:91 | ❌ GBPUSD_otc: Cannot run the event loop while another loop is running
2025-06-23 07:50:59 | ERROR    | __main__:test_parallel_historical_data:91 | ❌ USDJPY_otc: Cannot run the event loop while another loop is running
2025-06-23 07:50:59 | ERROR    | __main__:test_parallel_historical_data:91 | ❌ AUDCAD_otc: Cannot run the event loop while another loop is running
2025-06-23 07:50:59 | ERROR    | __main__:test_parallel_historical_data:91 | ❌ CADJPY_otc: Cannot run the event loop while another loop is running
2025-06-23 07:50:59 | INFO     | __main__:test_parallel_historical_data:103 | 📊 النتائج: 0/5 أزواج نجحت
2025-06-23 07:50:59 | INFO     | __main__:test_parallel_historical_data:104 | ⏱️ الوقت الإجمالي: 0.02 ثانية
2025-06-23 07:54:13 | INFO     | utils.logger:initialize:112 | تم تهيئة نظام السجلات بنجاح
2025-06-23 07:54:13 | INFO     | __main__:initialize:35 | 🚀 تهيئة اختبار جمع البيانات المتوازي
2025-06-23 07:54:13 | INFO     | config.database:_setup_postgresql:75 | تم إعداد PostgreSQL بنجاح
2025-06-23 07:54:15 | INFO     | config.database:_setup_redis:106 | تم إعداد Redis بنجاح
2025-06-23 07:54:15 | INFO     | config.database:initialize:33 | تم تهيئة قواعد البيانات بنجاح
2025-06-23 07:54:22 | INFO     | __main__:initialize:46 | ✅ تم تهيئة النظام بنجاح
2025-06-23 07:54:22 | INFO     | __main__:test_parallel_historical_data:55 | 📊 بدء اختبار جمع البيانات التاريخية المتوازي
2025-06-23 07:54:22 | INFO     | __main__:test_parallel_historical_data:75 | ⏳ جمع البيانات لـ 5 أزواج بشكل متوازي...
2025-06-23 07:54:22 | INFO     | utils.logger:log_data_collection:168 | جمع البيانات - الزوج: EURUSD_otc, عدد الشموع: 0, الحالة: فشل - Cannot run the event loop while another loop is running
2025-06-23 07:54:22 | INFO     | utils.logger:log_data_collection:168 | جمع البيانات - الزوج: GBPUSD_otc, عدد الشموع: 0, الحالة: فشل - Cannot run the event loop while another loop is running
2025-06-23 07:54:22 | INFO     | utils.logger:log_data_collection:168 | جمع البيانات - الزوج: USDJPY_otc, عدد الشموع: 0, الحالة: فشل - Cannot run the event loop while another loop is running
2025-06-23 07:54:22 | INFO     | utils.logger:log_data_collection:168 | جمع البيانات - الزوج: AUDCAD_otc, عدد الشموع: 0, الحالة: فشل - Cannot run the event loop while another loop is running
2025-06-23 07:54:22 | INFO     | utils.logger:log_data_collection:168 | جمع البيانات - الزوج: CADJPY_otc, عدد الشموع: 0, الحالة: فشل - Cannot run the event loop while another loop is running
2025-06-23 07:54:22 | ERROR    | __main__:test_parallel_historical_data:91 | ❌ EURUSD_otc: Cannot run the event loop while another loop is running
2025-06-23 07:54:22 | ERROR    | __main__:test_parallel_historical_data:91 | ❌ GBPUSD_otc: Cannot run the event loop while another loop is running
2025-06-23 07:54:22 | ERROR    | __main__:test_parallel_historical_data:91 | ❌ USDJPY_otc: Cannot run the event loop while another loop is running
2025-06-23 07:54:22 | ERROR    | __main__:test_parallel_historical_data:91 | ❌ AUDCAD_otc: Cannot run the event loop while another loop is running
2025-06-23 07:54:22 | ERROR    | __main__:test_parallel_historical_data:91 | ❌ CADJPY_otc: Cannot run the event loop while another loop is running
2025-06-23 07:54:22 | INFO     | __main__:test_parallel_historical_data:103 | 📊 النتائج: 0/5 أزواج نجحت
2025-06-23 07:54:22 | INFO     | __main__:test_parallel_historical_data:104 | ⏱️ الوقت الإجمالي: 0.02 ثانية
2025-06-23 07:55:15 | INFO     | utils.logger:initialize:112 | تم تهيئة نظام السجلات بنجاح
2025-06-23 07:55:15 | INFO     | __main__:initialize:35 | 🚀 تهيئة اختبار جمع البيانات المتوازي
2025-06-23 07:55:15 | INFO     | config.database:_setup_postgresql:75 | تم إعداد PostgreSQL بنجاح
2025-06-23 07:55:17 | INFO     | config.database:_setup_redis:106 | تم إعداد Redis بنجاح
2025-06-23 07:55:17 | INFO     | config.database:initialize:33 | تم تهيئة قواعد البيانات بنجاح
2025-06-23 07:55:24 | INFO     | __main__:initialize:46 | ✅ تم تهيئة النظام بنجاح
2025-06-23 07:55:24 | INFO     | __main__:test_parallel_historical_data:55 | 📊 بدء اختبار جمع البيانات التاريخية
2025-06-23 07:55:24 | INFO     | __main__:test_parallel_historical_data:69 | ⏳ جمع البيانات لـ 5 أزواج...
2025-06-23 07:55:24 | INFO     | utils.logger:log_data_collection:168 | جمع البيانات - الزوج: EURUSD_otc, عدد الشموع: 0, الحالة: فشل - Cannot run the event loop while another loop is running
2025-06-23 07:55:24 | INFO     | utils.logger:log_data_collection:168 | جمع البيانات - الزوج: GBPUSD_otc, عدد الشموع: 0, الحالة: فشل - Cannot run the event loop while another loop is running
2025-06-23 07:55:24 | INFO     | utils.logger:log_data_collection:168 | جمع البيانات - الزوج: USDJPY_otc, عدد الشموع: 0, الحالة: فشل - Cannot run the event loop while another loop is running
2025-06-23 07:55:24 | INFO     | utils.logger:log_data_collection:168 | جمع البيانات - الزوج: AUDCAD_otc, عدد الشموع: 0, الحالة: فشل - Cannot run the event loop while another loop is running
2025-06-23 07:55:24 | INFO     | utils.logger:log_data_collection:168 | جمع البيانات - الزوج: CADJPY_otc, عدد الشموع: 0, الحالة: فشل - Cannot run the event loop while another loop is running
2025-06-23 07:55:24 | ERROR    | __main__:test_parallel_historical_data:84 | ❌ EURUSD_otc: Cannot run the event loop while another loop is running
2025-06-23 07:55:24 | ERROR    | __main__:test_parallel_historical_data:84 | ❌ GBPUSD_otc: Cannot run the event loop while another loop is running
2025-06-23 07:55:24 | ERROR    | __main__:test_parallel_historical_data:84 | ❌ USDJPY_otc: Cannot run the event loop while another loop is running
2025-06-23 07:55:24 | ERROR    | __main__:test_parallel_historical_data:84 | ❌ AUDCAD_otc: Cannot run the event loop while another loop is running
2025-06-23 07:55:24 | ERROR    | __main__:test_parallel_historical_data:84 | ❌ CADJPY_otc: Cannot run the event loop while another loop is running
2025-06-23 07:55:24 | INFO     | __main__:test_parallel_historical_data:97 | 📊 النتائج: 0/5 أزواج نجحت
2025-06-23 07:55:24 | INFO     | __main__:test_parallel_historical_data:98 | ⏱️ الوقت الإجمالي: 0.01 ثانية
