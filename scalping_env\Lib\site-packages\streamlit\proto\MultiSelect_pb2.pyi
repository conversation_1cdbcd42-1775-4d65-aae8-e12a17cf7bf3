"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
*!
Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

import builtins
import collections.abc
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.message
import streamlit.proto.LabelVisibilityMessage_pb2
import typing

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class MultiSelect(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ID_FIELD_NUMBER: builtins.int
    LABEL_FIELD_NUMBER: builtins.int
    DEFAULT_FIELD_NUMBER: builtins.int
    OPTIONS_FIELD_NUMBER: builtins.int
    HELP_FIELD_NUMBER: builtins.int
    FORM_ID_FIELD_NUMBER: builtins.int
    VALUE_FIELD_NUMBER: builtins.int
    RAW_VALUES_FIELD_NUMBER: builtins.int
    SET_VALUE_FIELD_NUMBER: builtins.int
    DISABLED_FIELD_NUMBER: builtins.int
    LABEL_VISIBILITY_FIELD_NUMBER: builtins.int
    MAX_SELECTIONS_FIELD_NUMBER: builtins.int
    PLACEHOLDER_FIELD_NUMBER: builtins.int
    ACCEPT_NEW_OPTIONS_FIELD_NUMBER: builtins.int
    id: builtins.str
    label: builtins.str
    help: builtins.str
    form_id: builtins.str
    set_value: builtins.bool
    disabled: builtins.bool
    max_selections: builtins.int
    placeholder: builtins.str
    accept_new_options: builtins.bool
    @property
    def default(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    @property
    def options(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    @property
    def value(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    @property
    def raw_values(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]:
        """after we introduced accept_new_options, we send the option as a string
        instead of an index to keep it simple.
        """

    @property
    def label_visibility(self) -> streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage: ...
    def __init__(
        self,
        *,
        id: builtins.str = ...,
        label: builtins.str = ...,
        default: collections.abc.Iterable[builtins.int] | None = ...,
        options: collections.abc.Iterable[builtins.str] | None = ...,
        help: builtins.str = ...,
        form_id: builtins.str = ...,
        value: collections.abc.Iterable[builtins.int] | None = ...,
        raw_values: collections.abc.Iterable[builtins.str] | None = ...,
        set_value: builtins.bool = ...,
        disabled: builtins.bool = ...,
        label_visibility: streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage | None = ...,
        max_selections: builtins.int = ...,
        placeholder: builtins.str = ...,
        accept_new_options: builtins.bool | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["_accept_new_options", b"_accept_new_options", "accept_new_options", b"accept_new_options", "label_visibility", b"label_visibility"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["_accept_new_options", b"_accept_new_options", "accept_new_options", b"accept_new_options", "default", b"default", "disabled", b"disabled", "form_id", b"form_id", "help", b"help", "id", b"id", "label", b"label", "label_visibility", b"label_visibility", "max_selections", b"max_selections", "options", b"options", "placeholder", b"placeholder", "raw_values", b"raw_values", "set_value", b"set_value", "value", b"value"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["_accept_new_options", b"_accept_new_options"]) -> typing.Literal["accept_new_options"] | None: ...

global___MultiSelect = MultiSelect
