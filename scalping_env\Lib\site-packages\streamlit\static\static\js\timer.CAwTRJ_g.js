import{q as k}from"./value.CgPGBV_l.js";const L=Math.PI,N=2*L,v=1e-6,et=N-v;function W(t){this._+=t[0];for(let n=1,e=t.length;n<e;++n)this._+=arguments[n]+t[n]}function it(t){let n=Math.floor(t);if(!(n>=0))throw new Error(`invalid digits: ${t}`);if(n>15)return W;const e=10**n;return function(i){this._+=i[0];for(let r=1,a=i.length;r<a;++r)this._+=Math.round(arguments[r]*e)/e+i[r]}}class B{constructor(n){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=n==null?W:it(n)}moveTo(n,e){this._append`M${this._x0=this._x1=+n},${this._y0=this._y1=+e}`}closePath(){this._x1!==null&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(n,e){this._append`L${this._x1=+n},${this._y1=+e}`}quadraticCurveTo(n,e,i,r){this._append`Q${+n},${+e},${this._x1=+i},${this._y1=+r}`}bezierCurveTo(n,e,i,r,a,u){this._append`C${+n},${+e},${+i},${+r},${this._x1=+a},${this._y1=+u}`}arcTo(n,e,i,r,a){if(n=+n,e=+e,i=+i,r=+r,a=+a,a<0)throw new Error(`negative radius: ${a}`);let u=this._x1,c=this._y1,f=i-n,s=r-e,o=u-n,l=c-e,h=o*o+l*l;if(this._x1===null)this._append`M${this._x1=n},${this._y1=e}`;else if(h>v)if(!(Math.abs(l*f-s*o)>v)||!a)this._append`L${this._x1=n},${this._y1=e}`;else{let p=i-u,_=r-c,w=f*f+s*s,$=p*p+_*_,m=Math.sqrt(w),g=Math.sqrt(h),y=a*Math.tan((L-Math.acos((w+h-$)/(2*m*g)))/2),M=y/g,d=y/m;Math.abs(M-1)>v&&this._append`L${n+M*o},${e+M*l}`,this._append`A${a},${a},0,0,${+(l*p>o*_)},${this._x1=n+d*f},${this._y1=e+d*s}`}}arc(n,e,i,r,a,u){if(n=+n,e=+e,i=+i,u=!!u,i<0)throw new Error(`negative radius: ${i}`);let c=i*Math.cos(r),f=i*Math.sin(r),s=n+c,o=e+f,l=1^u,h=u?r-a:a-r;this._x1===null?this._append`M${s},${o}`:(Math.abs(this._x1-s)>v||Math.abs(this._y1-o)>v)&&this._append`L${s},${o}`,i&&(h<0&&(h=h%N+N),h>et?this._append`A${i},${i},0,1,${l},${n-c},${e-f}A${i},${i},0,1,${l},${this._x1=s},${this._y1=o}`:h>v&&this._append`A${i},${i},0,${+(h>=L)},${l},${this._x1=n+i*Math.cos(a)},${this._y1=e+i*Math.sin(a)}`)}rect(n,e,i,r){this._append`M${this._x0=this._x1=+n},${this._y0=this._y1=+e}h${i=+i}v${+r}h${-i}Z`}toString(){return this._}}function rt(){return new B}rt.prototype=B.prototype;var Z=180/Math.PI,F={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function G(t,n,e,i,r,a){var u,c,f;return(u=Math.sqrt(t*t+n*n))&&(t/=u,n/=u),(f=t*e+n*i)&&(e-=t*f,i-=n*f),(c=Math.sqrt(e*e+i*i))&&(e/=c,i/=c,f/=c),t*i<n*e&&(t=-t,n=-n,f=-f,u=-u),{translateX:r,translateY:a,rotate:Math.atan2(n,t)*Z,skewX:Math.atan(f)*Z,scaleX:u,scaleY:c}}var A;function st(t){const n=new(typeof DOMMatrix=="function"?DOMMatrix:WebKitCSSMatrix)(t+"");return n.isIdentity?F:G(n.a,n.b,n.c,n.d,n.e,n.f)}function ot(t){return t==null||(A||(A=document.createElementNS("http://www.w3.org/2000/svg","g")),A.setAttribute("transform",t),!(t=A.transform.baseVal.consolidate()))?F:(t=t.matrix,G(t.a,t.b,t.c,t.d,t.e,t.f))}function J(t,n,e,i){function r(s){return s.length?s.pop()+" ":""}function a(s,o,l,h,p,_){if(s!==l||o!==h){var w=p.push("translate(",null,n,null,e);_.push({i:w-4,x:k(s,l)},{i:w-2,x:k(o,h)})}else(l||h)&&p.push("translate("+l+n+h+e)}function u(s,o,l,h){s!==o?(s-o>180?o+=360:o-s>180&&(s+=360),h.push({i:l.push(r(l)+"rotate(",null,i)-2,x:k(s,o)})):o&&l.push(r(l)+"rotate("+o+i)}function c(s,o,l,h){s!==o?h.push({i:l.push(r(l)+"skewX(",null,i)-2,x:k(s,o)}):o&&l.push(r(l)+"skewX("+o+i)}function f(s,o,l,h,p,_){if(s!==l||o!==h){var w=p.push(r(p)+"scale(",null,",",null,")");_.push({i:w-4,x:k(s,l)},{i:w-2,x:k(o,h)})}else(l!==1||h!==1)&&p.push(r(p)+"scale("+l+","+h+")")}return function(s,o){var l=[],h=[];return s=t(s),o=t(o),a(s.translateX,s.translateY,o.translateX,o.translateY,l,h),u(s.rotate,o.rotate,l,h),c(s.skewX,o.skewX,l,h),f(s.scaleX,s.scaleY,o.scaleX,o.scaleY,l,h),s=o=null,function(p){for(var _=-1,w=h.length,$;++_<w;)l[($=h[_]).i]=$.x(p);return l.join("")}}}var vt=J(st,"px, ","px)","deg)"),xt=J(ot,", ",")",")"),at=1e-12;function H(t){return((t=Math.exp(t))+1/t)/2}function lt(t){return((t=Math.exp(t))-1/t)/2}function ht(t){return((t=Math.exp(2*t))-1)/(t+1)}const gt=function t(n,e,i){function r(a,u){var c=a[0],f=a[1],s=a[2],o=u[0],l=u[1],h=u[2],p=o-c,_=l-f,w=p*p+_*_,$,m;if(w<at)m=Math.log(h/s)/n,$=function(X){return[c+X*p,f+X*_,s*Math.exp(n*X*m)]};else{var g=Math.sqrt(w),y=(h*h-s*s+i*w)/(2*s*e*g),M=(h*h-s*s-i*w)/(2*h*e*g),d=Math.log(Math.sqrt(y*y+1)-y),nt=Math.log(Math.sqrt(M*M+1)-M);m=(nt-d)/n,$=function(X){var j=X*m,z=H(d),Q=s/(e*g)*(z*ht(n*j+d)-lt(d));return[c+Q*p,f+Q*_,s*z/H(n*j+d)]}}return $.duration=m*1e3*n/Math.SQRT2,$}return r.rho=function(a){var u=Math.max(.001,+a),c=u*u,f=c*c;return t(u,c,f)},r}(Math.SQRT2,2,4);var ut={value:()=>{}};function ft(){for(var t=0,n=arguments.length,e={},i;t<n;++t){if(!(i=arguments[t]+"")||i in e||/[\s.]/.test(i))throw new Error("illegal type: "+i);e[i]=[]}return new I(e)}function I(t){this._=t}function ct(t,n){return t.trim().split(/^|\s+/).map(function(e){var i="",r=e.indexOf(".");if(r>=0&&(i=e.slice(r+1),e=e.slice(0,r)),e&&!n.hasOwnProperty(e))throw new Error("unknown type: "+e);return{type:e,name:i}})}I.prototype=ft.prototype={constructor:I,on:function(t,n){var e=this._,i=ct(t+"",e),r,a=-1,u=i.length;if(arguments.length<2){for(;++a<u;)if((r=(t=i[a]).type)&&(r=pt(e[r],t.name)))return r;return}if(n!=null&&typeof n!="function")throw new Error("invalid callback: "+n);for(;++a<u;)if(r=(t=i[a]).type)e[r]=K(e[r],t.name,n);else if(n==null)for(r in e)e[r]=K(e[r],t.name,null);return this},copy:function(){var t={},n=this._;for(var e in n)t[e]=n[e].slice();return new I(t)},call:function(t,n){if((r=arguments.length-2)>0)for(var e=new Array(r),i=0,r,a;i<r;++i)e[i]=arguments[i+2];if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(a=this._[t],i=0,r=a.length;i<r;++i)a[i].value.apply(n,e)},apply:function(t,n,e){if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(var i=this._[t],r=0,a=i.length;r<a;++r)i[r].value.apply(n,e)}};function pt(t,n){for(var e=0,i=t.length,r;e<i;++e)if((r=t[e]).name===n)return r.value}function K(t,n,e){for(var i=0,r=t.length;i<r;++i)if(t[i].name===n){t[i]=ut,t=t.slice(0,i).concat(t.slice(i+1));break}return e!=null&&t.push({name:n,value:e}),t}var T=0,E=0,q=0,U=1e3,O,S,C=0,x=0,D=0,Y=typeof performance=="object"&&performance.now?performance:Date,b=typeof window=="object"&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(t){setTimeout(t,17)};function tt(){return x||(b(_t),x=Y.now()+D)}function _t(){x=0}function P(){this._call=this._time=this._next=null}P.prototype=wt.prototype={constructor:P,restart:function(t,n,e){if(typeof t!="function")throw new TypeError("callback is not a function");e=(e==null?tt():+e)+(n==null?0:+n),!this._next&&S!==this&&(S?S._next=this:O=this,S=this),this._call=t,this._time=e,R()},stop:function(){this._call&&(this._call=null,this._time=1/0,R())}};function wt(t,n,e){var i=new P;return i.restart(t,n,e),i}function $t(){tt(),++T;for(var t=O,n;t;)(n=x-t._time)>=0&&t._call.call(void 0,n),t=t._next;--T}function V(){x=(C=Y.now())+D,T=E=0;try{$t()}finally{T=0,dt(),x=0}}function mt(){var t=Y.now(),n=t-C;n>U&&(D-=n,C=t)}function dt(){for(var t,n=O,e,i=1/0;n;)n._call?(i>n._time&&(i=n._time),t=n,n=n._next):(e=n._next,n._next=null,n=t?t._next=e:O=e);S=t,R(i)}function R(t){if(!T){E&&(E=clearTimeout(E));var n=t-x;n>24?(t<1/0&&(E=setTimeout(V,t-Y.now()-D)),q&&(q=clearInterval(q))):(q||(C=Y.now(),q=setInterval(mt,U)),T=1,b(V))}}export{B as P,P as T,xt as a,gt as b,ft as d,vt as i,tt as n,rt as p,wt as t};
