"""
عميل الاتصال بمنصة Pocket Option
Pocket Option Client
"""
import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from BinaryOptionsToolsV2.pocketoption import PocketOptionAsync

from config.settings import get_current_ssid, pocket_option_config
from utils.logger import get_logger, log_data_collection
from utils.helpers import retry_async

logger = get_logger(__name__)

class PocketOptionClient:
    """عميل الاتصال بمنصة Pocket Option"""
    
    def __init__(self):
        self.ssid = get_current_ssid()
        self.api = None
        self.connected = False
        self.connection_attempts = 0
        self.max_connection_attempts = 5
        self.reconnect_delay = 10
        
    async def connect(self) -> bool:
        """الاتصال بالمنصة"""
        try:
            logger.info("🔌 محاولة الاتصال بمنصة Pocket Option...")
            
            # إنشاء عميل الاتصال
            self.api = PocketOptionAsync(self.ssid)
            
            # انتظار تأسيس الاتصال
            await asyncio.sleep(5)
            
            # اختبار الاتصال
            if await self._test_connection():
                self.connected = True
                self.connection_attempts = 0
                account_type = "تجريبي" if pocket_option_config.use_demo_account else "حقيقي"
                logger.info(f"✅ تم الاتصال بنجاح - الحساب: {account_type}")
                return True
            else:
                logger.error("❌ فشل في اختبار الاتصال")
                return False
                
        except Exception as e:
            logger.error(f"❌ خطأ في الاتصال: {e}")
            self.connected = False
            return False
    
    async def _test_connection(self) -> bool:
        """اختبار الاتصال"""
        try:
            # اختبار بسيط - جلب نسب الأرباح
            payouts = await self.api.payout()
            if payouts and len(payouts) > 0:
                logger.info(f"📊 تم جلب نسب الأرباح: {len(payouts)} أصل")
                return True

            # إذا فشل، جرب جلب الرصيد
            balance = await self.api.balance()
            if balance is not None:
                logger.info(f"💰 الرصيد الحالي: ${balance}")
                return True

            return False
        except Exception as e:
            logger.error(f"خطأ في اختبار الاتصال: {e}")
            return False
    
    async def ensure_connected(self) -> bool:
        """التأكد من الاتصال وإعادة الاتصال إذا لزم الأمر"""
        if not self.connected:
            return await self.reconnect()
        return True
    
    async def reconnect(self) -> bool:
        """إعادة الاتصال"""
        if self.connection_attempts >= self.max_connection_attempts:
            logger.error(f"❌ تجاوز الحد الأقصى لمحاولات الاتصال ({self.max_connection_attempts})")
            return False
        
        self.connection_attempts += 1
        logger.info(f"🔄 إعادة الاتصال - المحاولة {self.connection_attempts}/{self.max_connection_attempts}")
        
        await asyncio.sleep(self.reconnect_delay)
        return await self.connect()
    
    @retry_async(max_attempts=3, delay=2.0)
    async def get_balance(self) -> Optional[float]:
        """الحصول على الرصيد"""
        try:
            if not await self.ensure_connected():
                return None
            
            balance = await self.api.balance()
            return float(balance) if balance is not None else None
            
        except Exception as e:
            logger.error(f"خطأ في جلب الرصيد: {e}")
            self.connected = False
            return None
    
    @retry_async(max_attempts=3, delay=2.0)
    async def get_payouts(self) -> Optional[Dict[str, float]]:
        """الحصول على نسب الأرباح"""
        try:
            if not await self.ensure_connected():
                return None
            
            payouts = await self.api.payout()
            return payouts if payouts else None
            
        except Exception as e:
            logger.error(f"خطأ في جلب نسب الأرباح: {e}")
            self.connected = False
            return None
    
    @retry_async(max_attempts=3, delay=2.0)
    async def get_candles(self, asset: str, timeframe: int, period: int) -> Optional[List[Dict[str, Any]]]:
        """جلب البيانات التاريخية"""
        try:
            if not await self.ensure_connected():
                return None
            
            candles = await self.api.get_candles(asset, timeframe, period)
            
            if candles and len(candles) > 0:
                log_data_collection(asset, len(candles), "نجح")
                return candles
            else:
                log_data_collection(asset, 0, "فشل - لا توجد بيانات")
                return None
                
        except Exception as e:
            logger.error(f"خطأ في جلب بيانات {asset}: {e}")
            log_data_collection(asset, 0, f"فشل - {str(e)}")
            self.connected = False
            return None
    
    async def get_multiple_candles(self, assets: List[str], timeframe: int, period: int) -> Dict[str, List[Dict[str, Any]]]:
        """جلب البيانات التاريخية لعدة أصول بشكل متوازي"""
        try:
            if not await self.ensure_connected():
                return {}
            
            logger.info(f"📊 جلب البيانات لـ {len(assets)} أصل بشكل متوازي...")
            
            # إنشاء مهام متوازية
            tasks = []
            for asset in assets:
                task = asyncio.create_task(self.get_candles(asset, timeframe, period))
                tasks.append(task)
            
            # تنفيذ جميع المهام
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # تجميع النتائج
            candles_data = {}
            for i, result in enumerate(results):
                asset = assets[i]
                if isinstance(result, Exception):
                    logger.error(f"❌ خطأ في جلب بيانات {asset}: {result}")
                    candles_data[asset] = []
                elif result:
                    candles_data[asset] = result
                else:
                    candles_data[asset] = []
            
            successful_assets = sum(1 for data in candles_data.values() if len(data) > 0)
            logger.info(f"📈 نجح جلب البيانات لـ {successful_assets}/{len(assets)} أصل")
            
            return candles_data
            
        except Exception as e:
            logger.error(f"خطأ في جلب البيانات المتوازية: {e}")
            return {}
    
    @retry_async(max_attempts=3, delay=2.0)
    async def place_trade(self, asset: str, amount: float, direction: str, duration: int) -> Optional[Dict[str, Any]]:
        """تنفيذ صفقة"""
        try:
            if not await self.ensure_connected():
                return None
            
            # تحويل الاتجاه
            action = "call" if direction.upper() == "CALL" else "put"
            
            # تنفيذ الصفقة
            result = await self.api.buy(asset, amount, action, duration)
            
            if result:
                logger.info(f"✅ تم تنفيذ صفقة {direction} على {asset} بمبلغ ${amount}")
                return result
            else:
                logger.error(f"❌ فشل في تنفيذ صفقة {direction} على {asset}")
                return None
                
        except Exception as e:
            logger.error(f"خطأ في تنفيذ الصفقة: {e}")
            self.connected = False
            return None
    
    async def start_live_stream(self, assets: List[str], callback: Callable[[str, Dict[str, Any]], None]) -> bool:
        """بدء البث المباشر للأسعار"""
        try:
            if not await self.ensure_connected():
                return False
            
            logger.info(f"📡 بدء البث المباشر لـ {len(assets)} أصل...")
            
            # هنا يمكن إضافة منطق البث المباشر
            # حالياً سنستخدم polling كبديل
            asyncio.create_task(self._polling_loop(assets, callback))
            
            return True
            
        except Exception as e:
            logger.error(f"خطأ في بدء البث المباشر: {e}")
            return False
    
    async def _polling_loop(self, assets: List[str], callback: Callable[[str, Dict[str, Any]], None]):
        """حلقة polling للبيانات المباشرة"""
        while self.connected:
            try:
                for asset in assets:
                    # جلب آخر شمعة
                    candles = await self.get_candles(asset, 300, 300)  # شمعة واحدة
                    if candles and len(candles) > 0:
                        latest_candle = candles[-1]
                        callback(asset, latest_candle)
                
                # انتظار 5 ثواني قبل التحديث التالي
                await asyncio.sleep(5)
                
            except Exception as e:
                logger.error(f"خطأ في polling loop: {e}")
                await asyncio.sleep(10)
    
    async def disconnect(self):
        """قطع الاتصال"""
        try:
            self.connected = False
            if self.api:
                # إغلاق الاتصال إذا كان متاحاً
                pass
            logger.info("🔌 تم قطع الاتصال بالمنصة")
        except Exception as e:
            logger.error(f"خطأ في قطع الاتصال: {e}")
    
    def is_connected(self) -> bool:
        """فحص حالة الاتصال"""
        return self.connected
    
    def get_connection_info(self) -> Dict[str, Any]:
        """الحصول على معلومات الاتصال"""
        return {
            'connected': self.connected,
            'connection_attempts': self.connection_attempts,
            'account_type': 'demo' if pocket_option_config.use_demo_account else 'real',
            'ssid_length': len(self.ssid) if self.ssid else 0
        }
