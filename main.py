"""
نقطة الدخول الرئيسية لنظام تداول السكالبينغ
Main Entry Point for Scalping Trading System
"""
import asyncio
import signal
import sys
from pathlib import Path
from typing import Optional

# إضافة المجلد الحالي إلى مسار Python
sys.path.append(str(Path(__file__).parent))

from config.database import initialize_databases, test_database_connections, close_database_connections
from config.settings import system_config, pocket_option_config
from utils.logger import get_logger, log_system_performance
from utils.helpers import create_directories

logger = get_logger(__name__)

class ScalpingTradingSystem:
    """النظام الرئيسي للتداول"""
    
    def __init__(self):
        self.running = False
        self.components = {}
        
    async def initialize(self):
        """تهيئة النظام"""
        try:
            logger.info("🚀 بدء تهيئة نظام تداول السكالبينغ")
            
            # إنشاء المجلدات المطلوبة
            await self._create_directories()
            
            # تهيئة قواعد البيانات
            logger.info("📊 تهيئة قواعد البيانات...")
            await initialize_databases()
            
            # اختبار الاتصالات
            logger.info("🔍 اختبار اتصالات قواعد البيانات...")
            connections = await test_database_connections()
            
            if not all(connections.values()):
                failed_connections = [k for k, v in connections.items() if not v]
                raise RuntimeError(f"فشل في الاتصال بـ: {', '.join(failed_connections)}")
            
            logger.info("✅ تم تهيئة النظام بنجاح")
            
        except Exception as e:
            logger.error(f"❌ خطأ في تهيئة النظام: {e}")
            raise
    
    async def _create_directories(self):
        """إنشاء المجلدات المطلوبة"""
        directories = [
            "logs",
            "data",
            "ai_predictor/models",
            "database/migrations",
            "web_interface/components",
            "web_interface/pages",
            "tests"
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
        
        logger.info("📁 تم إنشاء جميع المجلدات المطلوبة")
    
    async def start_components(self):
        """بدء تشغيل مكونات النظام"""
        try:
            logger.info("🔧 بدء تشغيل مكونات النظام...")

            # تشغيل طبقة جمع البيانات
            await self._start_data_collector()

            self.running = True
            logger.info("✅ تم بدء تشغيل جميع المكونات")

        except Exception as e:
            logger.error(f"❌ خطأ في بدء تشغيل المكونات: {e}")
            raise

    async def _start_data_collector(self):
        """بدء تشغيل طبقة جمع البيانات"""
        try:
            from data_collector.data_manager import DataManager

            logger.info("📊 بدء تشغيل طبقة جمع البيانات...")

            # إنشاء مدير البيانات
            data_manager = DataManager()

            # تهيئة مدير البيانات
            if await data_manager.initialize():
                # بدء جمع البيانات
                if await data_manager.start_data_collection():
                    self.components['data_collector'] = data_manager
                    logger.info("✅ تم تشغيل طبقة جمع البيانات بنجاح")

                    # عرض حالة النظام
                    status = data_manager.get_status()
                    logger.info(f"📈 الأزواج النشطة: {len(status['active_pairs'])}")
                    logger.info(f"🔌 حالة الاتصال: {'متصل' if status['connected'] else 'غير متصل'}")
                else:
                    raise RuntimeError("فشل في بدء جمع البيانات")
            else:
                raise RuntimeError("فشل في تهيئة مدير البيانات")

        except Exception as e:
            logger.error(f"❌ خطأ في تشغيل طبقة جمع البيانات: {e}")
            raise
    
    async def stop_components(self):
        """إيقاف مكونات النظام"""
        try:
            logger.info("🛑 إيقاف مكونات النظام...")
            
            self.running = False
            
            # إيقاف جميع المكونات
            for component_name, component in self.components.items():
                try:
                    if hasattr(component, 'stop'):
                        await component.stop()
                    logger.info(f"✅ تم إيقاف {component_name}")
                except Exception as e:
                    logger.error(f"❌ خطأ في إيقاف {component_name}: {e}")
            
            # إغلاق اتصالات قواعد البيانات
            await close_database_connections()
            
            logger.info("✅ تم إيقاف جميع المكونات")
            
        except Exception as e:
            logger.error(f"❌ خطأ في إيقاف النظام: {e}")
    
    async def run(self):
        """تشغيل النظام الرئيسي"""
        try:
            await self.initialize()
            await self.start_components()
            
            logger.info("🎯 النظام يعمل الآن...")
            logger.info(f"📊 نوع الحساب: {'تجريبي' if pocket_option_config.use_demo_account else 'حقيقي'}")
            logger.info(f"🌐 واجهة الويب: http://localhost:{system_config.web_port}")
            
            # الحلقة الرئيسية
            while self.running:
                await asyncio.sleep(1)
                
        except KeyboardInterrupt:
            logger.info("⏹️ تم إيقاف النظام بواسطة المستخدم")
        except Exception as e:
            logger.error(f"❌ خطأ في تشغيل النظام: {e}")
        finally:
            await self.stop_components()

# إنشاء مثيل النظام
trading_system = ScalpingTradingSystem()

def signal_handler(signum, frame):
    """معالج إشارات النظام"""
    logger.info(f"تم استلام إشارة {signum}")
    trading_system.running = False

async def main():
    """الدالة الرئيسية"""
    # تسجيل معالجات الإشارات
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        await trading_system.run()
    except Exception as e:
        logger.error(f"خطأ في النظام الرئيسي: {e}")
        sys.exit(1)

if __name__ == "__main__":
    # تشغيل النظام
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("تم إنهاء النظام")
    except Exception as e:
        logger.error(f"خطأ غير متوقع: {e}")
        sys.exit(1)
