"""
مستودعات البيانات
Data Repositories
"""
import asyncio
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from sqlalchemy import desc, asc, and_, or_, func
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from .models import CurrencyPair, Candle, TechnicalIndicator, Trade, RiskSetting, SystemLog, PerformanceMetric
from utils.logger import get_logger

logger = get_logger(__name__)

class CurrencyPairRepository:
    """مستودع أزواج العملات"""
    
    def __init__(self, session: Session):
        self.session = session
    
    def create(self, symbol: str, name: str = None) -> CurrencyPair:
        """إنشاء زوج عملة جديد"""
        pair = CurrencyPair(symbol=symbol, name=name)
        self.session.add(pair)
        self.session.commit()
        self.session.refresh(pair)
        return pair
    
    def get_by_symbol(self, symbol: str) -> Optional[CurrencyPair]:
        """الحصول على زوج عملة بالرمز"""
        return self.session.query(CurrencyPair).filter(CurrencyPair.symbol == symbol).first()
    
    def get_all_active(self) -> List[CurrencyPair]:
        """الحصول على جميع الأزواج النشطة"""
        return self.session.query(CurrencyPair).filter(CurrencyPair.is_active == True).all()
    
    def get_or_create(self, symbol: str, name: str = None) -> CurrencyPair:
        """الحصول على زوج عملة أو إنشاؤه إذا لم يكن موجوداً"""
        pair = self.get_by_symbol(symbol)
        if not pair:
            pair = self.create(symbol, name)
        return pair
    
    def deactivate(self, symbol: str) -> bool:
        """إلغاء تفعيل زوج عملة"""
        pair = self.get_by_symbol(symbol)
        if pair:
            pair.is_active = False
            self.session.commit()
            return True
        return False

class CandleRepository:
    """مستودع الشموع"""
    
    def __init__(self, session: Session):
        self.session = session
    
    def create(self, pair_id: int, timestamp: datetime, open_price: float, 
               high_price: float, low_price: float, close_price: float,
               volume: float = 0, timeframe: int = 300) -> Candle:
        """إنشاء شمعة جديدة"""
        candle = Candle(
            pair_id=pair_id,
            timestamp=timestamp,
            open_price=open_price,
            high_price=high_price,
            low_price=low_price,
            close_price=close_price,
            volume=volume,
            timeframe=timeframe
        )
        self.session.add(candle)
        self.session.commit()
        self.session.refresh(candle)
        return candle
    
    def get_latest_candles(self, pair_id: int, limit: int = 100, timeframe: int = 300) -> List[Candle]:
        """الحصول على أحدث الشموع"""
        return (self.session.query(Candle)
                .filter(and_(Candle.pair_id == pair_id, Candle.timeframe == timeframe))
                .order_by(desc(Candle.timestamp))
                .limit(limit)
                .all())
    
    def get_candles_in_range(self, pair_id: int, start_time: datetime, 
                           end_time: datetime, timeframe: int = 300) -> List[Candle]:
        """الحصول على الشموع في فترة زمنية محددة"""
        return (self.session.query(Candle)
                .filter(and_(
                    Candle.pair_id == pair_id,
                    Candle.timeframe == timeframe,
                    Candle.timestamp >= start_time,
                    Candle.timestamp <= end_time
                ))
                .order_by(asc(Candle.timestamp))
                .all())
    
    def get_candle_count(self, pair_id: int, timeframe: int = 300) -> int:
        """الحصول على عدد الشموع لزوج معين"""
        return (self.session.query(Candle)
                .filter(and_(Candle.pair_id == pair_id, Candle.timeframe == timeframe))
                .count())
    
    def delete_old_candles(self, pair_id: int, keep_count: int = 2500, timeframe: int = 300) -> int:
        """حذف الشموع القديمة والاحتفاظ بعدد محدد"""
        # الحصول على الشموع القديمة
        old_candles = (self.session.query(Candle)
                      .filter(and_(Candle.pair_id == pair_id, Candle.timeframe == timeframe))
                      .order_by(desc(Candle.timestamp))
                      .offset(keep_count)
                      .all())
        
        deleted_count = len(old_candles)
        for candle in old_candles:
            self.session.delete(candle)
        
        self.session.commit()
        return deleted_count
    
    def bulk_create(self, candles_data: List[Dict[str, Any]]) -> List[Candle]:
        """إنشاء عدة شموع دفعة واحدة"""
        candles = []
        for data in candles_data:
            candle = Candle(**data)
            candles.append(candle)
            self.session.add(candle)
        
        self.session.commit()
        return candles

class TechnicalIndicatorRepository:
    """مستودع المؤشرات الفنية"""
    
    def __init__(self, session: Session):
        self.session = session
    
    def create(self, candle_id: int, indicator_name: str, value: float,
               extra_data: Dict[str, Any] = None) -> TechnicalIndicator:
        """إنشاء مؤشر فني جديد"""
        indicator = TechnicalIndicator(
            candle_id=candle_id,
            indicator_name=indicator_name,
            value=value,
            extra_data=extra_data
        )
        self.session.add(indicator)
        self.session.commit()
        self.session.refresh(indicator)
        return indicator
    
    def get_by_candle_and_name(self, candle_id: int, indicator_name: str) -> Optional[TechnicalIndicator]:
        """الحصول على مؤشر بالشمعة والاسم"""
        return (self.session.query(TechnicalIndicator)
                .filter(and_(
                    TechnicalIndicator.candle_id == candle_id,
                    TechnicalIndicator.indicator_name == indicator_name
                ))
                .first())
    
    def get_indicators_for_candles(self, candle_ids: List[int], 
                                 indicator_names: List[str] = None) -> List[TechnicalIndicator]:
        """الحصول على المؤشرات لعدة شموع"""
        query = self.session.query(TechnicalIndicator).filter(TechnicalIndicator.candle_id.in_(candle_ids))
        
        if indicator_names:
            query = query.filter(TechnicalIndicator.indicator_name.in_(indicator_names))
        
        return query.all()
    
    def bulk_create(self, indicators_data: List[Dict[str, Any]]) -> List[TechnicalIndicator]:
        """إنشاء عدة مؤشرات دفعة واحدة"""
        indicators = []
        for data in indicators_data:
            indicator = TechnicalIndicator(**data)
            indicators.append(indicator)
            self.session.add(indicator)
        
        self.session.commit()
        return indicators

class TradeRepository:
    """مستودع الصفقات"""
    
    def __init__(self, session: Session):
        self.session = session
    
    def create(self, pair_id: int, trade_type: str, amount: float, entry_price: float,
               entry_time: datetime, expiry_time: datetime, confidence_score: float,
               technical_signals: Dict[str, Any] = None, behavioral_signals: Dict[str, Any] = None,
               ai_prediction: Dict[str, Any] = None, platform_trade_id: str = None) -> Trade:
        """إنشاء صفقة جديدة"""
        trade = Trade(
            pair_id=pair_id,
            trade_type=trade_type,
            amount=amount,
            entry_price=entry_price,
            entry_time=entry_time,
            expiry_time=expiry_time,
            confidence_score=confidence_score,
            technical_signals=technical_signals,
            behavioral_signals=behavioral_signals,
            ai_prediction=ai_prediction,
            platform_trade_id=platform_trade_id
        )
        self.session.add(trade)
        self.session.commit()
        self.session.refresh(trade)
        return trade
    
    def update_result(self, trade_id: int, exit_price: float, result: str, profit_loss: float) -> bool:
        """تحديث نتيجة الصفقة"""
        trade = self.session.query(Trade).filter(Trade.id == trade_id).first()
        if trade:
            trade.exit_price = exit_price
            trade.result = result
            trade.profit_loss = profit_loss
            self.session.commit()
            return True
        return False
    
    def get_recent_trades(self, limit: int = 100) -> List[Trade]:
        """الحصول على أحدث الصفقات"""
        return (self.session.query(Trade)
                .order_by(desc(Trade.entry_time))
                .limit(limit)
                .all())
    
    def get_trades_by_pair(self, pair_id: int, limit: int = 100) -> List[Trade]:
        """الحصول على صفقات زوج معين"""
        return (self.session.query(Trade)
                .filter(Trade.pair_id == pair_id)
                .order_by(desc(Trade.entry_time))
                .limit(limit)
                .all())
    
    def get_performance_stats(self, days: int = 30) -> Dict[str, Any]:
        """الحصول على إحصائيات الأداء"""
        start_date = datetime.now() - timedelta(days=days)
        
        trades = (self.session.query(Trade)
                 .filter(Trade.entry_time >= start_date)
                 .all())
        
        if not trades:
            return {
                'total_trades': 0,
                'win_rate': 0,
                'total_profit': 0,
                'avg_profit_per_trade': 0
            }
        
        total_trades = len(trades)
        winning_trades = len([t for t in trades if t.result == 'WIN'])
        total_profit = sum([float(t.profit_loss) for t in trades if t.profit_loss])
        
        return {
            'total_trades': total_trades,
            'win_rate': (winning_trades / total_trades) * 100 if total_trades > 0 else 0,
            'total_profit': total_profit,
            'avg_profit_per_trade': total_profit / total_trades if total_trades > 0 else 0,
            'winning_trades': winning_trades,
            'losing_trades': total_trades - winning_trades
        }

class RiskSettingRepository:
    """مستودع إعدادات إدارة المخاطر"""
    
    def __init__(self, session: Session):
        self.session = session
    
    def get_setting(self, setting_name: str) -> Optional[RiskSetting]:
        """الحصول على إعداد بالاسم"""
        return self.session.query(RiskSetting).filter(RiskSetting.setting_name == setting_name).first()
    
    def set_setting(self, setting_name: str, setting_value: Dict[str, Any], description: str = None) -> RiskSetting:
        """تعيين إعداد"""
        setting = self.get_setting(setting_name)
        if setting:
            setting.setting_value = setting_value
            setting.description = description
            setting.updated_at = datetime.now()
        else:
            setting = RiskSetting(
                setting_name=setting_name,
                setting_value=setting_value,
                description=description
            )
            self.session.add(setting)
        
        self.session.commit()
        self.session.refresh(setting)
        return setting
    
    def get_all_settings(self) -> List[RiskSetting]:
        """الحصول على جميع الإعدادات"""
        return self.session.query(RiskSetting).all()
